import { NextResponse } from 'next/server';
import connectDB from '../../../lib/mongodb';
import GalleryItem from '../../../models/GalleryItem';
import { getCurrentUser, requireAuth, requireManager } from '../../../lib/middleware';

// GET /api/gallery - List gallery items
export async function GET(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 12;
    const skip = (page - 1) * limit;
    
    // Build filter object
    const filter = {};
    
    // Search by title or artist
    const search = searchParams.get('search');
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { 'artist.name': { $regex: search, $options: 'i' } },
        { 'seo.tags': { $in: [new RegExp(search, 'i')] } },
      ];
    }
    
    // Filter by category
    const category = searchParams.get('category');
    if (category && category !== 'all') {
      filter.category = category;
    }
    
    // Filter by status
    const status = searchParams.get('status');
    if (status && status !== 'all') {
      filter.status = status;
    } else {
      // Default to available items for public
      filter.status = { $in: ['available', 'reserved'] };
    }
    
    // Filter by price range
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    if (minPrice || maxPrice) {
      filter['pricing.price'] = {};
      if (minPrice) filter['pricing.price'].$gte = parseFloat(minPrice);
      if (maxPrice) filter['pricing.price'].$lte = parseFloat(maxPrice);
    }
    
    // Filter by availability
    const forSale = searchParams.get('forSale');
    if (forSale === 'true') {
      filter['pricing.isForSale'] = true;
    }
    
    // Featured items
    const featured = searchParams.get('featured');
    if (featured === 'true') {
      filter['featured.isFeatured'] = true;
    }
    
    // Sort options
    const sort = searchParams.get('sort') || 'createdAt';
    const order = searchParams.get('order') === 'asc' ? 1 : -1;
    let sortObj = { [sort]: order };
    
    // Special sorting for featured items
    if (featured === 'true') {
      sortObj = { 'featured.featuredOrder': 1, ...sortObj };
    }
    
    const items = await GalleryItem.find(filter)
      .sort(sortObj)
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'name email');
    
    const total = await GalleryItem.countDocuments(filter);
    
    return NextResponse.json({
      items,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching gallery items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch gallery items' },
      { status: 500 }
    );
  }
}

// POST /api/gallery - Create new gallery item (manager/admin only)
export async function POST(request) {
  const authCheck = await requireManager(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const user = await getCurrentUser();
    const data = await request.json();
    
    // Validate required fields
    const requiredFields = ['title', 'description', 'artist', 'category', 'medium', 'pricing'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }
    
    // Create gallery item
    const item = new GalleryItem({
      ...data,
      createdBy: user.id,
    });
    
    await item.save();
    await item.populate('createdBy', 'name email');
    
    return NextResponse.json(item, { status: 201 });
  } catch (error) {
    console.error('Error creating gallery item:', error);
    return NextResponse.json(
      { error: 'Failed to create gallery item' },
      { status: 500 }
    );
  }
}
