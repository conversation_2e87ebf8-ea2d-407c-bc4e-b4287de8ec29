'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';

const AdminLodgesPage = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [lodges, setLodges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({});

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session || !['manager', 'admin'].includes(session.user?.role)) {
      router.push('/');
      return;
    }
    
    fetchLodges();
  }, [session, status, router, currentPage, searchTerm, selectedType]);

  const fetchLodges = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
      });
      
      if (searchTerm) params.append('search', searchTerm);
      if (selectedType !== 'all') params.append('type', selectedType);
      
      const response = await fetch(`/api/lodges?${params}`);
      const data = await response.json();
      
      if (response.ok) {
        setLodges(data.lodges);
        setPagination(data.pagination);
      } else {
        console.error('Error fetching lodges:', data.error);
      }
    } catch (error) {
      console.error('Error fetching lodges:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (lodgeId) => {
    if (!confirm('Are you sure you want to delete this lodge?')) return;
    
    try {
      const response = await fetch(`/api/lodges/${lodgeId}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        fetchLodges(); // Refresh the list
      } else {
        const data = await response.json();
        alert(data.error || 'Failed to delete lodge');
      }
    } catch (error) {
      console.error('Error deleting lodge:', error);
      alert('Failed to delete lodge');
    }
  };

  const toggleStatus = async (lodgeId, currentStatus) => {
    try {
      const response = await fetch(`/api/lodges/${lodgeId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          availability: {
            isActive: !currentStatus,
          },
        }),
      });
      
      if (response.ok) {
        fetchLodges(); // Refresh the list
      } else {
        const data = await response.json();
        alert(data.error || 'Failed to update lodge status');
      }
    } catch (error) {
      console.error('Error updating lodge status:', error);
      alert('Failed to update lodge status');
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session || !['manager', 'admin'].includes(session.user?.role)) {
    return null;
  }

  const lodgeTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'cabin', label: 'Cabins' },
    { value: 'lodge', label: 'Lodges' },
    { value: 'villa', label: 'Villas' },
    { value: 'suite', label: 'Suites' },
    { value: 'room', label: 'Rooms' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Manage Lodges</h1>
            <p className="text-gray-600 mt-2">Create, edit, and manage your lodge listings</p>
          </div>
          <Link href="/admin/lodges/new">
            <Button>Add New Lodge</Button>
          </Link>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                placeholder="Search lodges..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                label="Search"
              />
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Lodge Type
                </label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {lodgeTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Lodges List */}
        <div className="space-y-4">
          {lodges.map(lodge => (
            <Card key={lodge._id}>
              <CardContent className="pt-6">
                <div className="flex items-start justify-between">
                  <div className="flex space-x-4">
                    {lodge.images && lodge.images.length > 0 && (
                      <img
                        src={lodge.images[0].url}
                        alt={lodge.images[0].alt}
                        className="w-24 h-24 object-cover rounded-lg"
                      />
                    )}
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900">{lodge.name}</h3>
                      <p className="text-gray-600 capitalize">{lodge.type}</p>
                      <p className="text-sm text-gray-500">
                        {lodge.location.city}, {lodge.location.state}
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        {lodge.capacity.guests} guests • {lodge.capacity.bedrooms} bedrooms • {lodge.capacity.bathrooms} bathrooms
                      </p>
                      <div className="flex items-center mt-2">
                        <span className="text-lg font-bold text-blue-600">
                          ${lodge.pricing.basePrice}/night
                        </span>
                        <span className={`ml-4 px-2 py-1 rounded-full text-xs font-medium ${
                          lodge.availability.isActive 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {lodge.availability.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Link href={`/admin/lodges/${lodge._id}/edit`}>
                      <Button variant="outline" size="sm">Edit</Button>
                    </Link>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleStatus(lodge._id, lodge.availability.isActive)}
                    >
                      {lodge.availability.isActive ? 'Deactivate' : 'Activate'}
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(lodge._id)}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {lodges.length === 0 && !loading && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <p className="text-gray-500 text-lg">No lodges found</p>
                <p className="text-gray-400 mt-2">Create your first lodge to get started</p>
                <Link href="/admin/lodges/new">
                  <Button className="mt-4">Add New Lodge</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex space-x-2">
              <Button
                variant="outline"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
              >
                Previous
              </Button>
              <span className="flex items-center px-4 py-2 text-sm text-gray-700">
                Page {currentPage} of {pagination.pages}
              </span>
              <Button
                variant="outline"
                disabled={currentPage === pagination.pages}
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminLodgesPage;
