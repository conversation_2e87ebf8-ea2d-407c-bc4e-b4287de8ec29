import { NextResponse } from 'next/server';
import connectDB from '../../../../lib/mongodb';
import User from '../../../../models/User';
import { getCurrentUser, requireAuth, requireAdmin } from '../../../../lib/middleware';

// GET /api/users/[id] - Get single user
export async function GET(request, { params }) {
  const authCheck = await requireAuth(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const currentUser = await getCurrentUser();
    const { id } = params;
    
    // Users can only view their own profile unless they're admin
    if (id !== currentUser.id && !currentUser.hasRole('admin')) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }
    
    const user = await User.findById(id)
      .select('-accounts -sessions')
      .populate('bookings', 'bookingNumber status dates pricing')
      .populate('orders', 'orderNumber status pricing createdAt');
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

// PUT /api/users/[id] - Update user
export async function PUT(request, { params }) {
  const authCheck = await requireAuth(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const currentUser = await getCurrentUser();
    const { id } = params;
    const data = await request.json();
    
    // Users can only update their own profile unless they're admin
    if (id !== currentUser.id && !currentUser.hasRole('admin')) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }
    
    const user = await User.findById(id);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Only admins can change roles
    if (data.role && !currentUser.hasRole('admin')) {
      delete data.role;
    }
    
    // Prevent users from changing sensitive fields
    if (id !== currentUser.id || !currentUser.hasRole('admin')) {
      delete data.email;
      delete data.emailVerified;
    }
    
    // Update user
    Object.assign(user, data);
    await user.save();
    
    // Remove sensitive data from response
    const userResponse = user.toObject();
    delete userResponse.accounts;
    delete userResponse.sessions;
    
    return NextResponse.json(userResponse);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

// DELETE /api/users/[id] - Delete user (admin only)
export async function DELETE(request, { params }) {
  const authCheck = await requireAdmin(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const { id } = params;
    
    const user = await User.findById(id);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Check if user has active bookings or orders
    const activeBookings = await user.populate('bookings');
    const hasActiveBookings = activeBookings.bookings.some(booking => 
      ['pending', 'confirmed', 'checked_in'].includes(booking.status)
    );
    
    if (hasActiveBookings) {
      return NextResponse.json(
        { error: 'Cannot delete user with active bookings' },
        { status: 400 }
      );
    }
    
    await User.findByIdAndDelete(id);
    
    return NextResponse.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
