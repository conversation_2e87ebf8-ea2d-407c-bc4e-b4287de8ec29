import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { stripe } from '../../../../lib/stripe';
import connectDB from '../../../../lib/mongodb';
import Booking from '../../../../models/Booking';
import Order from '../../../../models/Order';
import GalleryItem from '../../../../models/GalleryItem';

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request) {
  try {
    const body = await request.text();
    const signature = headers().get('stripe-signature');
    
    if (!signature || !webhookSecret) {
      return NextResponse.json(
        { error: 'Missing signature or webhook secret' },
        { status: 400 }
      );
    }
    
    let event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err.message);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }
    
    await connectDB();
    
    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;
        
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;
        
      case 'charge.dispute.created':
        await handleChargeDispute(event.data.object);
        break;
        
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
    
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}

async function handlePaymentSucceeded(paymentIntent) {
  const { metadata } = paymentIntent;
  
  try {
    if (metadata.type === 'booking') {
      // Update booking status
      const booking = await Booking.findById(metadata.bookingId);
      if (booking) {
        booking.payment.status = 'paid';
        booking.payment.paidAt = new Date();
        booking.payment.stripePaymentIntentId = paymentIntent.id;
        booking.payment.stripeChargeId = paymentIntent.latest_charge;
        booking.status = 'confirmed';
        await booking.save();
        
        console.log(`Booking ${booking.bookingNumber} payment confirmed`);
      }
    } else if (metadata.type === 'order') {
      // Update order status and mark gallery items as sold
      const order = await Order.findById(metadata.orderId).populate('items.galleryItem');
      if (order) {
        order.payment.status = 'paid';
        order.payment.paidAt = new Date();
        order.payment.stripePaymentIntentId = paymentIntent.id;
        order.payment.stripeChargeId = paymentIntent.latest_charge;
        order.status = 'confirmed';
        await order.save();
        
        // Update gallery items
        for (const item of order.items) {
          const galleryItem = await GalleryItem.findById(item.galleryItem);
          if (galleryItem) {
            if (galleryItem.inventory.isUnique) {
              galleryItem.markAsSold();
            } else {
              galleryItem.inventory.quantity -= item.quantity;
              if (galleryItem.inventory.quantity <= 0) {
                galleryItem.status = 'sold';
              }
            }
            await galleryItem.save();
          }
        }
        
        console.log(`Order ${order.orderNumber} payment confirmed`);
      }
    }
  } catch (error) {
    console.error('Error handling payment success:', error);
  }
}

async function handlePaymentFailed(paymentIntent) {
  const { metadata } = paymentIntent;
  
  try {
    if (metadata.type === 'booking') {
      const booking = await Booking.findById(metadata.bookingId);
      if (booking) {
        booking.payment.status = 'failed';
        booking.status = 'cancelled';
        await booking.save();
        
        console.log(`Booking ${booking.bookingNumber} payment failed`);
      }
    } else if (metadata.type === 'order') {
      const order = await Order.findById(metadata.orderId);
      if (order) {
        order.payment.status = 'failed';
        order.status = 'cancelled';
        await order.save();
        
        // Release reserved gallery items
        for (const item of order.items) {
          const galleryItem = await GalleryItem.findById(item.galleryItem);
          if (galleryItem && galleryItem.status === 'reserved') {
            galleryItem.status = 'available';
            await galleryItem.save();
          }
        }
        
        console.log(`Order ${order.orderNumber} payment failed`);
      }
    }
  } catch (error) {
    console.error('Error handling payment failure:', error);
  }
}

async function handleChargeDispute(dispute) {
  // Log dispute for manual review
  console.log('Charge dispute created:', {
    id: dispute.id,
    amount: dispute.amount,
    reason: dispute.reason,
    status: dispute.status,
    charge: dispute.charge,
  });
  
  // Here you could implement additional logic like:
  // - Sending notifications to admins
  // - Updating order/booking status
  // - Creating internal tickets for review
}
