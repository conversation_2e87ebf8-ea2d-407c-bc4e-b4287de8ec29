'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

const GalleryForm = ({ itemId = null }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    artist: {
      name: '',
      bio: '',
      website: '',
      contact: '',
    },
    category: 'painting',
    medium: '',
    dimensions: {
      width: 0,
      height: 0,
      depth: 0,
      unit: 'inches',
    },
    pricing: {
      price: 0,
      currency: 'USD',
      isForSale: true,
      originalPrice: 0,
      discount: 0,
    },
    images: [],
    inventory: {
      quantity: 1,
      isUnique: true,
    },
    details: {
      yearCreated: new Date().getFullYear(),
      materials: [],
      techniques: [],
      style: '',
      theme: '',
      isFramed: false,
      weight: 0,
      careInstructions: '',
    },
    shipping: {
      isShippable: true,
      shippingCost: 0,
      handlingTime: 3,
    },
    status: 'available',
    featured: {
      isFeatured: false,
      featuredOrder: 0,
    },
    seo: {
      tags: [],
    },
  });

  const categories = [
    { value: 'painting', label: 'Painting' },
    { value: 'sculpture', label: 'Sculpture' },
    { value: 'photography', label: 'Photography' },
    { value: 'digital_art', label: 'Digital Art' },
    { value: 'mixed_media', label: 'Mixed Media' },
    { value: 'crafts', label: 'Crafts' },
    { value: 'jewelry', label: 'Jewelry' },
    { value: 'textiles', label: 'Textiles' },
  ];

  const statuses = [
    { value: 'available', label: 'Available' },
    { value: 'sold', label: 'Sold' },
    { value: 'reserved', label: 'Reserved' },
    { value: 'on_hold', label: 'On Hold' },
    { value: 'discontinued', label: 'Discontinued' },
  ];

  useEffect(() => {
    if (itemId) {
      fetchGalleryItem();
    }
  }, [itemId]);

  const fetchGalleryItem = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/gallery/${itemId}`);
      const data = await response.json();

      if (response.ok) {
        setFormData(data);
      } else {
        console.error('Error fetching gallery item:', data.error);
      }
    } catch (error) {
      console.error('Error fetching gallery item:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value,
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleArrayChange = (field, value) => {
    const items = value.split(',').map(item => item.trim()).filter(item => item);
    setFormData(prev => ({
      ...prev,
      [field]: items,
    }));
  };

  const handleImageUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    try {
      setUploading(true);
      const formDataUpload = new FormData();
      files.forEach(file => formDataUpload.append('files', file));
      formDataUpload.append('type', 'gallery');
      if (itemId) formDataUpload.append('entityId', itemId);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formDataUpload,
      });

      const data = await response.json();

      if (response.ok) {
        setFormData(prev => ({
          ...prev,
          images: [...prev.images, ...data.files],
        }));
      } else {
        alert(data.error || 'Failed to upload images');
      }
    } catch (error) {
      console.error('Error uploading images:', error);
      alert('Failed to upload images');
    } finally {
      setUploading(false);
    }
  };

  const removeImage = (index) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    try {
      setLoading(true);
      const url = itemId ? `/api/gallery/${itemId}` : '/api/gallery';
      const method = itemId ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok) {
        router.push('/admin/gallery');
      } else {
        alert(data.error || 'Failed to save gallery item');
      }
    } catch (error) {
      console.error('Error saving gallery item:', error);
      alert('Failed to save gallery item');
    } finally {
      setLoading(false);
    }
  };

  if (loading && itemId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            {itemId ? 'Edit Artwork' : 'Add New Artwork'}
          </h1>
          <p className="text-gray-600 mt-2">
            {itemId ? 'Update artwork information' : 'Add a new piece to your gallery'}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>Essential details about the artwork</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                label="Title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                required
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  {categories.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>

              <Input
                label="Medium"
                value={formData.medium}
                onChange={(e) => handleInputChange('medium', e.target.value)}
                placeholder="e.g., Oil on Canvas, Digital Photography, Bronze Sculpture"
                required
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={4}
                  required
                />
              </div>
            </CardContent>
          </Card>

          {/* Artist Information */}
          <Card>
            <CardHeader>
              <CardTitle>Artist Information</CardTitle>
              <CardDescription>Details about the artist</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                label="Artist Name"
                value={formData.artist.name}
                onChange={(e) => handleInputChange('artist.name', e.target.value)}
                required
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Artist Bio
                </label>
                <textarea
                  value={formData.artist.bio}
                  onChange={(e) => handleInputChange('artist.bio', e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Website"
                  type="url"
                  value={formData.artist.website}
                  onChange={(e) => handleInputChange('artist.website', e.target.value)}
                />
                <Input
                  label="Contact"
                  value={formData.artist.contact}
                  onChange={(e) => handleInputChange('artist.contact', e.target.value)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Dimensions & Pricing */}
          <Card>
            <CardHeader>
              <CardTitle>Dimensions & Pricing</CardTitle>
              <CardDescription>Physical dimensions and pricing information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Input
                  label="Width"
                  type="number"
                  min="0"
                  step="0.1"
                  value={formData.dimensions.width}
                  onChange={(e) => handleInputChange('dimensions.width', parseFloat(e.target.value))}
                />
                <Input
                  label="Height"
                  type="number"
                  min="0"
                  step="0.1"
                  value={formData.dimensions.height}
                  onChange={(e) => handleInputChange('dimensions.height', parseFloat(e.target.value))}
                />
                <Input
                  label="Depth"
                  type="number"
                  min="0"
                  step="0.1"
                  value={formData.dimensions.depth}
                  onChange={(e) => handleInputChange('dimensions.depth', parseFloat(e.target.value))}
                />
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Unit</label>
                  <select
                    value={formData.dimensions.unit}
                    onChange={(e) => handleInputChange('dimensions.unit', e.target.value)}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="inches">Inches</option>
                    <option value="cm">Centimeters</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Input
                  label="Price ($)"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.pricing.price}
                  onChange={(e) => handleInputChange('pricing.price', parseFloat(e.target.value))}
                  required
                />
                <Input
                  label="Original Price ($)"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.pricing.originalPrice}
                  onChange={(e) => handleInputChange('pricing.originalPrice', parseFloat(e.target.value))}
                />
                <Input
                  label="Discount (%)"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.pricing.discount}
                  onChange={(e) => handleInputChange('pricing.discount', parseInt(e.target.value))}
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isForSale"
                  checked={formData.pricing.isForSale}
                  onChange={(e) => handleInputChange('pricing.isForSale', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="isForSale" className="text-sm font-medium text-gray-700">
                  Available for sale
                </label>
              </div>
            </CardContent>
          </Card>

          {/* Images */}
          <Card>
            <CardHeader>
              <CardTitle>Images</CardTitle>
              <CardDescription>Upload photos of the artwork</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  disabled={uploading}
                />
                {uploading && <p className="text-sm text-blue-600 mt-2">Uploading images...</p>}
              </div>

              {formData.images.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {formData.images.map((image, index) => (
                    <div key={index} className="relative">
                      <img
                        src={image.url}
                        alt={image.alt}
                        className="w-full h-32 object-cover rounded-lg"
                      />
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600"
                      >
                        ×
                      </button>
                      {index === 0 && (
                        <span className="absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                          Primary
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Details & Inventory */}
          <Card>
            <CardHeader>
              <CardTitle>Details & Inventory</CardTitle>
              <CardDescription>Additional artwork details and inventory settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Input
                  label="Year Created"
                  type="number"
                  min="1000"
                  max={new Date().getFullYear()}
                  value={formData.details.yearCreated}
                  onChange={(e) => handleInputChange('details.yearCreated', parseInt(e.target.value))}
                />
                <Input
                  label="Weight (lbs)"
                  type="number"
                  min="0"
                  step="0.1"
                  value={formData.details.weight}
                  onChange={(e) => handleInputChange('details.weight', parseFloat(e.target.value))}
                />
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <select
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {statuses.map(status => (
                      <option key={status.value} value={status.value}>
                        {status.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Materials (comma-separated)"
                  value={formData.details.materials.join(', ')}
                  onChange={(e) => handleArrayChange('details.materials', e.target.value)}
                  placeholder="e.g., Oil paint, Canvas, Wood frame"
                />
                <Input
                  label="Techniques (comma-separated)"
                  value={formData.details.techniques.join(', ')}
                  onChange={(e) => handleArrayChange('details.techniques', e.target.value)}
                  placeholder="e.g., Impasto, Glazing, Palette knife"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Style"
                  value={formData.details.style}
                  onChange={(e) => handleInputChange('details.style', e.target.value)}
                  placeholder="e.g., Abstract, Realism, Impressionism"
                />
                <Input
                  label="Theme"
                  value={formData.details.theme}
                  onChange={(e) => handleInputChange('details.theme', e.target.value)}
                  placeholder="e.g., Nature, Portrait, Landscape"
                />
              </div>

              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isFramed"
                    checked={formData.details.isFramed}
                    onChange={(e) => handleInputChange('details.isFramed', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="isFramed" className="text-sm font-medium text-gray-700">
                    Framed
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isUnique"
                    checked={formData.inventory.isUnique}
                    onChange={(e) => handleInputChange('inventory.isUnique', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="isUnique" className="text-sm font-medium text-gray-700">
                    One-of-a-kind piece
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isFeatured"
                    checked={formData.featured.isFeatured}
                    onChange={(e) => handleInputChange('featured.isFeatured', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="isFeatured" className="text-sm font-medium text-gray-700">
                    Featured artwork
                  </label>
                </div>
              </div>

              {!formData.inventory.isUnique && (
                <Input
                  label="Quantity Available"
                  type="number"
                  min="0"
                  value={formData.inventory.quantity}
                  onChange={(e) => handleInputChange('inventory.quantity', parseInt(e.target.value))}
                />
              )}
            </CardContent>
          </Card>

          {/* Shipping & SEO */}
          <Card>
            <CardHeader>
              <CardTitle>Shipping & SEO</CardTitle>
              <CardDescription>Shipping options and search optimization</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2 mb-4">
                <input
                  type="checkbox"
                  id="isShippable"
                  checked={formData.shipping.isShippable}
                  onChange={(e) => handleInputChange('shipping.isShippable', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="isShippable" className="text-sm font-medium text-gray-700">
                  Available for shipping
                </label>
              </div>

              {formData.shipping.isShippable && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Shipping Cost ($)"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.shipping.shippingCost}
                    onChange={(e) => handleInputChange('shipping.shippingCost', parseFloat(e.target.value))}
                  />
                  <Input
                    label="Handling Time (days)"
                    type="number"
                    min="1"
                    value={formData.shipping.handlingTime}
                    onChange={(e) => handleInputChange('shipping.handlingTime', parseInt(e.target.value))}
                  />
                </div>
              )}

              <Input
                label="SEO Tags (comma-separated)"
                value={formData.seo.tags.join(', ')}
                onChange={(e) => handleArrayChange('seo.tags', e.target.value)}
                placeholder="e.g., modern art, abstract painting, blue artwork"
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Care Instructions
                </label>
                <textarea
                  value={formData.details.careInstructions}
                  onChange={(e) => handleInputChange('details.careInstructions', e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Instructions for caring for and maintaining the artwork..."
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/admin/gallery')}
            >
              Cancel
            </Button>
            <Button type="submit" loading={loading}>
              {itemId ? 'Update Artwork' : 'Add Artwork'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default GalleryForm;
