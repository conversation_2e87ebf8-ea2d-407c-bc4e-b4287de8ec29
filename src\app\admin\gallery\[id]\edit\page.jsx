'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import GalleryForm from '../../../../../components/admin/GalleryForm';

const EditGalleryItemPage = ({ params }) => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { id } = params;

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session || !['manager', 'admin'].includes(session.user?.role)) {
      router.push('/');
      return;
    }
  }, [session, status, router]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session || !['manager', 'admin'].includes(session.user?.role)) {
    return null;
  }

  return <GalleryForm itemId={id} />;
};

export default EditGalleryItemPage;
