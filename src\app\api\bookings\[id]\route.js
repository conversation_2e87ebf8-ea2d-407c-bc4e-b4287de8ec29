import { NextResponse } from 'next/server';
import connectDB from '../../../../lib/mongodb';
import Booking from '../../../../models/Booking';
import { getCurrentUser, requireAuth, requireManager } from '../../../../lib/middleware';
import { createRefund } from '../../../../lib/stripe';

// GET /api/bookings/[id] - Get single booking
export async function GET(request, { params }) {
  const authCheck = await requireAuth(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const user = await getCurrentUser();
    const { id } = params;
    
    let filter = { _id: id };
    
    // If not admin/manager, only allow access to own bookings
    if (!user.hasRole('manager')) {
      filter.user = user.id;
    }
    
    const booking = await Booking.findOne(filter)
      .populate('lodge', 'name location images pricing availability rules')
      .populate('user', 'name email');
    
    if (!booking) {
      return NextResponse.json(
        { error: 'Booking not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(booking);
  } catch (error) {
    console.error('Error fetching booking:', error);
    return NextResponse.json(
      { error: 'Failed to fetch booking' },
      { status: 500 }
    );
  }
}

// PUT /api/bookings/[id] - Update booking status (manager/admin only)
export async function PUT(request, { params }) {
  const authCheck = await requireManager(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const { id } = params;
    const data = await request.json();
    
    const booking = await Booking.findById(id);
    if (!booking) {
      return NextResponse.json(
        { error: 'Booking not found' },
        { status: 404 }
      );
    }
    
    // Update booking
    if (data.status) {
      booking.updateStatus(data.status);
    }
    
    if (data.checkInOut) {
      Object.assign(booking.checkInOut, data.checkInOut);
    }
    
    if (data.notes) {
      booking.notes.internal = data.notes;
    }
    
    await booking.save();
    await booking.populate([
      { path: 'lodge', select: 'name location images' },
      { path: 'user', select: 'name email' },
    ]);
    
    return NextResponse.json(booking);
  } catch (error) {
    console.error('Error updating booking:', error);
    return NextResponse.json(
      { error: 'Failed to update booking' },
      { status: 500 }
    );
  }
}

// DELETE /api/bookings/[id] - Cancel booking
export async function DELETE(request, { params }) {
  const authCheck = await requireAuth(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const user = await getCurrentUser();
    const { id } = params;
    
    let filter = { _id: id };
    
    // If not admin/manager, only allow cancellation of own bookings
    if (!user.hasRole('manager')) {
      filter.user = user.id;
    }
    
    const booking = await Booking.findOne(filter).populate('lodge');
    if (!booking) {
      return NextResponse.json(
        { error: 'Booking not found' },
        { status: 404 }
      );
    }
    
    // Check if booking can be cancelled
    if (!booking.canBeCancelled()) {
      return NextResponse.json(
        { error: 'Booking cannot be cancelled' },
        { status: 400 }
      );
    }
    
    // Calculate refund amount
    const refundAmount = booking.calculateRefund(booking.lodge.rules.cancellationPolicy);
    
    // Process refund if payment was made
    if (booking.payment.status === 'paid' && refundAmount > 0) {
      try {
        const refund = await createRefund(
          booking.payment.stripeChargeId,
          refundAmount,
          'requested_by_customer'
        );
        
        booking.payment.refundAmount = refundAmount;
        booking.payment.refundedAt = new Date();
        booking.payment.status = refundAmount === booking.pricing.total ? 'refunded' : 'partially_refunded';
      } catch (refundError) {
        console.error('Refund failed:', refundError);
        return NextResponse.json(
          { error: 'Failed to process refund' },
          { status: 500 }
        );
      }
    }
    
    // Update booking status
    booking.status = 'cancelled';
    booking.cancellation = {
      cancelledAt: new Date(),
      cancelledBy: user.id,
      reason: 'Cancelled by customer',
      refundAmount,
    };
    
    await booking.save();
    
    return NextResponse.json({
      message: 'Booking cancelled successfully',
      refundAmount,
    });
  } catch (error) {
    console.error('Error cancelling booking:', error);
    return NextResponse.json(
      { error: 'Failed to cancel booking' },
      { status: 500 }
    );
  }
}
