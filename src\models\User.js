import mongoose from 'mongoose';

const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
  },
  emailVerified: {
    type: Date,
  },
  image: {
    type: String,
  },
  role: {
    type: String,
    enum: ['guest', 'user', 'manager', 'admin'],
    default: 'user',
  },
  profile: {
    phone: String,
    address: {
      street1: String,
      street2: String,
      city: String,
      state: String,
      zipCode: String,
      country: { type: String, default: 'USA' },
    },
    preferences: {
      notifications: {
        email: { type: Boolean, default: true },
        sms: { type: Boolean, default: false },
      },
      newsletter: { type: Boolean, default: true },
    },
    isGuestCreated: { type: Boolean, default: false },
    lastLoginAt: Date,
    loginCount: { type: Number, default: 0 },
  },
  password: {
    type: String,
    select: false, // Don't include password in queries by default
  },
  accounts: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Account',
  }],
  sessions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Session',
  }],
  bookings: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
  }],
  orders: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
  }],
}, {
  timestamps: true,
});

// Indexes for better performance
UserSchema.index({ email: 1 });
UserSchema.index({ role: 1 });

// Virtual for full name
UserSchema.virtual('fullName').get(function() {
  return this.name;
});

// Method to check if user has specific role or higher
UserSchema.methods.hasRole = function(requiredRole) {
  const roleHierarchy = {
    guest: 0,
    user: 1,
    manager: 2,
    admin: 3,
  };

  return roleHierarchy[this.role] >= roleHierarchy[requiredRole];
};

// Method to check if user can manage lodges
UserSchema.methods.canManageLodges = function() {
  return this.hasRole('manager');
};

// Method to check if user can manage users
UserSchema.methods.canManageUsers = function() {
  return this.hasRole('admin');
};

// Method to track login activity
UserSchema.methods.trackLogin = function() {
  this.profile.lastLoginAt = new Date();
  this.profile.loginCount = (this.profile.loginCount || 0) + 1;
  return this.save();
};

// Method to check if user is a guest user
UserSchema.methods.isGuest = function() {
  return this.profile?.isGuestCreated === true && !this.password;
};

// Method to convert guest to full user
UserSchema.methods.convertToFullUser = function(password) {
  this.password = password;
  this.emailVerified = new Date();
  if (this.profile?.isGuestCreated) {
    this.profile.isGuestCreated = false;
  }
  return this.save();
};

export default mongoose.models.User || mongoose.model('User', UserSchema);
