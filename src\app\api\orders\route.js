import { NextResponse } from 'next/server';
import connectDB from '../../../lib/mongodb';
import Order from '../../../models/Order';
import GalleryItem from '../../../models/GalleryItem';
import { getCurrentUser, requireAuth, requireManager } from '../../../lib/middleware';
import { createOrderPaymentIntent } from '../../../lib/stripe';

// GET /api/orders - Get user orders or all orders (admin)
export async function GET(request) {
  const authCheck = await requireAuth(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const user = await getCurrentUser();
    const { searchParams } = new URL(request.url);
    
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const skip = (page - 1) * limit;
    
    let filter = {};
    
    // If not admin/manager, only show user's own orders
    if (!user.hasRole('manager')) {
      filter.user = user.id;
    }
    
    // Filter by status
    const status = searchParams.get('status');
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    const orders = await Order.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('user', 'name email')
      .populate('items.galleryItem', 'title artist images');
    
    const total = await Order.countDocuments(filter);
    
    return NextResponse.json({
      orders,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching orders:', error);
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}

// POST /api/orders - Create new order
export async function POST(request) {
  const authCheck = await requireAuth(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const user = await getCurrentUser();
    const data = await request.json();
    
    // Validate required fields
    const requiredFields = ['items', 'shipping', 'billing'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }
    
    if (!data.items || data.items.length === 0) {
      return NextResponse.json(
        { error: 'Order must contain at least one item' },
        { status: 400 }
      );
    }
    
    // Validate and process items
    let subtotal = 0;
    const orderItems = [];
    
    for (const item of data.items) {
      const galleryItem = await GalleryItem.findById(item.galleryItem);
      if (!galleryItem) {
        return NextResponse.json(
          { error: `Gallery item ${item.galleryItem} not found` },
          { status: 404 }
        );
      }
      
      if (!galleryItem.canBePurchased(item.quantity)) {
        return NextResponse.json(
          { error: `Item "${galleryItem.title}" is not available` },
          { status: 400 }
        );
      }
      
      const itemTotal = galleryItem.discountedPrice * item.quantity;
      subtotal += itemTotal;
      
      orderItems.push({
        galleryItem: galleryItem._id,
        quantity: item.quantity,
        price: galleryItem.discountedPrice,
        title: galleryItem.title,
        artist: galleryItem.artist.name,
        sku: galleryItem.inventory.sku,
      });
      
      // Reserve items
      galleryItem.reserve();
      await galleryItem.save();
    }
    
    // Calculate shipping and tax
    const shipping = data.shipping.method === 'pickup' ? 0 : 25; // Default shipping cost
    const taxRate = 0.08; // 8% tax rate
    const tax = (subtotal + shipping) * taxRate;
    const total = subtotal + shipping + tax;
    
    // Create order
    const order = new Order({
      user: user.id,
      items: orderItems,
      pricing: {
        subtotal,
        shipping,
        tax,
        total,
        currency: 'USD',
      },
      shipping: data.shipping,
      billing: data.billing,
      status: 'pending',
    });
    
    await order.save();
    
    // Create Stripe payment intent
    const paymentIntent = await createOrderPaymentIntent(order);
    
    // Update order with payment intent ID
    order.payment.stripePaymentIntentId = paymentIntent.id;
    await order.save();
    
    await order.populate([
      { path: 'user', select: 'name email' },
      { path: 'items.galleryItem', select: 'title artist images' },
    ]);
    
    return NextResponse.json({
      order,
      clientSecret: paymentIntent.client_secret,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating order:', error);
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    );
  }
}
