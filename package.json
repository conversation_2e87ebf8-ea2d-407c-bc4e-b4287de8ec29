{"name": "elephantislandtemplate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "verify-auth": "node scripts/verify-auth-config.js", "test-auth": "npm run verify-auth && echo 'Auth configuration verified!'"}, "dependencies": {"@auth/mongodb-adapter": "^3.9.1", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "firebase": "^10.13.0", "firebase-admin": "^12.3.1", "mongodb": "^6.16.0", "mongoose": "^8.15.1", "next": "15.3.2", "next-auth": "^5.0.0-beta.28", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.0.0", "stripe": "^16.12.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "dotenv": "^16.4.5", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4"}}