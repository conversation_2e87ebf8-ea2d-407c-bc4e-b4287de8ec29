import { initializeApp } from 'firebase/app';
import { getStorage, ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';

const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY,
  authDomain: process.env.FIREBASE_AUTH_DOMAIN,
  projectId: process.env.FIREBASE_PROJECT_ID,
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.FIREBASE_APP_ID,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const storage = getStorage(app);

/**
 * Upload file to Firebase Storage
 * @param {File} file - File to upload
 * @param {string} path - Storage path
 * @returns {Promise<string>} Download URL
 */
export async function uploadFile(file, path) {
  try {
    const storageRef = ref(storage, path);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);
    return downloadURL;
  } catch (error) {
    console.error('Error uploading file:', error);
    throw error;
  }
}

/**
 * Upload multiple files to Firebase Storage
 * @param {File[]} files - Files to upload
 * @param {string} basePath - Base storage path
 * @returns {Promise<string[]>} Array of download URLs
 */
export async function uploadMultipleFiles(files, basePath) {
  try {
    const uploadPromises = files.map((file, index) => {
      const fileName = `${Date.now()}_${index}_${file.name}`;
      const path = `${basePath}/${fileName}`;
      return uploadFile(file, path);
    });
    
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading multiple files:', error);
    throw error;
  }
}

/**
 * Delete file from Firebase Storage
 * @param {string} url - File URL to delete
 * @returns {Promise<void>}
 */
export async function deleteFile(url) {
  try {
    const fileRef = ref(storage, url);
    await deleteObject(fileRef);
  } catch (error) {
    console.error('Error deleting file:', error);
    throw error;
  }
}

/**
 * Upload lodge images
 * @param {File[]} files - Image files
 * @param {string} lodgeId - Lodge ID
 * @returns {Promise<Object[]>} Array of image objects
 */
export async function uploadLodgeImages(files, lodgeId) {
  const basePath = `lodges/${lodgeId}/images`;
  const urls = await uploadMultipleFiles(files, basePath);
  
  return urls.map((url, index) => ({
    url,
    alt: files[index].name,
    isPrimary: index === 0,
    order: index,
  }));
}

/**
 * Upload gallery item images
 * @param {File[]} files - Image files
 * @param {string} itemId - Gallery item ID
 * @returns {Promise<Object[]>} Array of image objects
 */
export async function uploadGalleryImages(files, itemId) {
  const basePath = `gallery/${itemId}/images`;
  const urls = await uploadMultipleFiles(files, basePath);
  
  return urls.map((url, index) => ({
    url,
    alt: files[index].name,
    isPrimary: index === 0,
    order: index,
  }));
}

/**
 * Upload user avatar
 * @param {File} file - Avatar file
 * @param {string} userId - User ID
 * @returns {Promise<string>} Avatar URL
 */
export async function uploadUserAvatar(file, userId) {
  const fileName = `avatar_${Date.now()}_${file.name}`;
  const path = `users/${userId}/${fileName}`;
  return await uploadFile(file, path);
}

/**
 * Generate unique filename
 * @param {string} originalName - Original filename
 * @returns {string} Unique filename
 */
export function generateUniqueFileName(originalName) {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2);
  const extension = originalName.split('.').pop();
  return `${timestamp}_${random}.${extension}`;
}

/**
 * Validate image file
 * @param {File} file - File to validate
 * @param {Object} options - Validation options
 * @returns {boolean} Is valid
 */
export function validateImageFile(file, options = {}) {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB default
    allowedTypes = ['image/jpeg', 'image/png', 'image/webp'],
  } = options;
  
  if (!allowedTypes.includes(file.type)) {
    throw new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`);
  }
  
  if (file.size > maxSize) {
    throw new Error(`File too large. Maximum size: ${maxSize / 1024 / 1024}MB`);
  }
  
  return true;
}

export { storage };
