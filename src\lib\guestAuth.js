import bcrypt from 'bcryptjs';
import connectDB from './mongodb';
import User from '../models/User';
import { generateId } from './utils';

/**
 * Create or find user for guest booking/purchase
 */
export async function createOrFindGuestUser(userData) {
  await connectDB();
  
  const { email, name, phone, firstName, lastName } = userData;
  
  // Check if user already exists
  let existingUser = await User.findOne({ email });
  
  if (existingUser) {
    // Update user information if provided
    if (name && !existingUser.name) {
      existingUser.name = name;
    }
    if (firstName && lastName && !existingUser.name) {
      existingUser.name = `${firstName} ${lastName}`;
    }
    if (phone && !existingUser.profile?.phone) {
      existingUser.profile = {
        ...existingUser.profile,
        phone,
      };
    }
    
    await existingUser.save();
    return existingUser;
  }
  
  // Create new user
  const fullName = name || (firstName && lastName ? `${firstName} ${lastName}` : email.split('@')[0]);
  
  // Determine role based on email
  const role = email === '<EMAIL>' ? 'admin' : 'user';
  
  const newUser = new User({
    name: fullName,
    email,
    role,
    profile: {
      phone: phone || '',
      isGuestCreated: true, // Flag to indicate this was created during guest flow
    },
    emailVerified: null, // Guest users are not email verified initially
  });
  
  await newUser.save();
  return newUser;
}

/**
 * Create user account with password (for checkout completion)
 */
export async function createUserAccountWithPassword(userData, password) {
  await connectDB();
  
  const { email, name, phone, address } = userData;
  
  // Check if user already exists
  let existingUser = await User.findOne({ email });
  
  if (existingUser) {
    // If user exists but doesn't have password, add it
    if (!existingUser.password && password) {
      const hashedPassword = await bcrypt.hash(password, 12);
      existingUser.password = hashedPassword;
      existingUser.emailVerified = new Date(); // Mark as verified when they set password
      
      // Update profile information
      if (phone) {
        existingUser.profile = {
          ...existingUser.profile,
          phone,
        };
      }
      
      if (address) {
        existingUser.profile = {
          ...existingUser.profile,
          address,
        };
      }
      
      await existingUser.save();
    }
    
    return existingUser;
  }
  
  // Create new user with password
  const hashedPassword = await bcrypt.hash(password, 12);
  
  // Determine role based on email
  const role = email === '<EMAIL>' ? 'admin' : 'user';
  
  const newUser = new User({
    name,
    email,
    password: hashedPassword,
    role,
    profile: {
      phone: phone || '',
      address: address || {},
    },
    emailVerified: new Date(),
  });
  
  await newUser.save();
  return newUser;
}

/**
 * Validate guest booking data
 */
export function validateGuestBookingData(data) {
  const errors = [];
  
  if (!data.guestInfo?.firstName?.trim()) {
    errors.push('First name is required');
  }
  
  if (!data.guestInfo?.lastName?.trim()) {
    errors.push('Last name is required');
  }
  
  if (!data.guestInfo?.email?.trim()) {
    errors.push('Email is required');
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.guestInfo.email)) {
    errors.push('Valid email is required');
  }
  
  if (!data.guestInfo?.phone?.trim()) {
    errors.push('Phone number is required');
  }
  
  if (!data.dates?.checkIn) {
    errors.push('Check-in date is required');
  }
  
  if (!data.dates?.checkOut) {
    errors.push('Check-out date is required');
  }
  
  if (!data.lodge) {
    errors.push('Lodge selection is required');
  }
  
  return errors;
}

/**
 * Validate guest order data
 */
export function validateGuestOrderData(data) {
  const errors = [];
  
  if (!data.billing?.address?.firstName?.trim()) {
    errors.push('First name is required');
  }
  
  if (!data.billing?.address?.lastName?.trim()) {
    errors.push('Last name is required');
  }
  
  if (!data.billing?.address?.email?.trim()) {
    errors.push('Email is required');
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.billing.address.email)) {
    errors.push('Valid email is required');
  }
  
  if (!data.billing?.address?.street1?.trim()) {
    errors.push('Street address is required');
  }
  
  if (!data.billing?.address?.city?.trim()) {
    errors.push('City is required');
  }
  
  if (!data.billing?.address?.state?.trim()) {
    errors.push('State is required');
  }
  
  if (!data.billing?.address?.zipCode?.trim()) {
    errors.push('ZIP code is required');
  }
  
  if (!data.items || data.items.length === 0) {
    errors.push('Order must contain at least one item');
  }
  
  return errors;
}

/**
 * Generate temporary session for guest users
 */
export function generateGuestSession(userData) {
  return {
    id: generateId(16),
    user: {
      email: userData.email,
      name: userData.name,
      isGuest: true,
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
  };
}

/**
 * Send welcome email to new users (placeholder)
 */
export async function sendWelcomeEmail(user, isGuestCreated = false) {
  // This would integrate with your email service
  console.log(`Welcome email would be sent to ${user.email}`);
  
  if (isGuestCreated) {
    console.log('Guest user created - sending account creation notification');
  }
  
  // TODO: Implement actual email sending
  // - Welcome message
  // - Account creation details
  // - Password setup link for guest users
  // - Booking/order confirmation
}

/**
 * Clean up old guest sessions
 */
export async function cleanupGuestSessions() {
  await connectDB();
  
  // Remove users created as guests who never completed registration
  // and have no bookings or orders after 30 days
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  
  await User.deleteMany({
    'profile.isGuestCreated': true,
    password: { $exists: false },
    emailVerified: null,
    createdAt: { $lt: thirtyDaysAgo },
    bookings: { $size: 0 },
    orders: { $size: 0 },
  });
}

/**
 * Convert guest user to full user account
 */
export async function convertGuestToUser(email, password, additionalData = {}) {
  await connectDB();
  
  const user = await User.findOne({ email });
  if (!user) {
    throw new Error('User not found');
  }
  
  if (user.password) {
    throw new Error('User already has an account');
  }
  
  const hashedPassword = await bcrypt.hash(password, 12);
  
  user.password = hashedPassword;
  user.emailVerified = new Date();
  
  // Update profile with additional data
  if (additionalData.phone) {
    user.profile.phone = additionalData.phone;
  }
  
  if (additionalData.address) {
    user.profile.address = additionalData.address;
  }
  
  // Remove guest flag
  if (user.profile.isGuestCreated) {
    delete user.profile.isGuestCreated;
  }
  
  await user.save();
  
  // Send welcome email
  await sendWelcomeEmail(user, false);
  
  return user;
}
