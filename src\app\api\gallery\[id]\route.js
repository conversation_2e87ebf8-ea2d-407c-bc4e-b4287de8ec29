import { NextResponse } from 'next/server';
import connectDB from '../../../../lib/mongodb';
import GalleryItem from '../../../../models/GalleryItem';
import Order from '../../../../models/Order';
import { getCurrentUser, requireManager } from '../../../../lib/middleware';

// GET /api/gallery/[id] - Get single gallery item
export async function GET(request, { params }) {
  try {
    await connectDB();
    const { id } = params;
    
    const item = await GalleryItem.findById(id).populate('createdBy', 'name email');
    
    if (!item) {
      return NextResponse.json(
        { error: 'Gallery item not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(item);
  } catch (error) {
    console.error('Error fetching gallery item:', error);
    return NextResponse.json(
      { error: 'Failed to fetch gallery item' },
      { status: 500 }
    );
  }
}

// PUT /api/gallery/[id] - Update gallery item (manager/admin only)
export async function PUT(request, { params }) {
  const authCheck = await requireManager(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const { id } = params;
    const data = await request.json();
    
    const item = await GalleryItem.findById(id);
    if (!item) {
      return NextResponse.json(
        { error: 'Gallery item not found' },
        { status: 404 }
      );
    }
    
    // Update item
    Object.assign(item, data);
    await item.save();
    await item.populate('createdBy', 'name email');
    
    return NextResponse.json(item);
  } catch (error) {
    console.error('Error updating gallery item:', error);
    return NextResponse.json(
      { error: 'Failed to update gallery item' },
      { status: 500 }
    );
  }
}

// DELETE /api/gallery/[id] - Delete gallery item (manager/admin only)
export async function DELETE(request, { params }) {
  const authCheck = await requireManager(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const { id } = params;
    
    const item = await GalleryItem.findById(id);
    if (!item) {
      return NextResponse.json(
        { error: 'Gallery item not found' },
        { status: 404 }
      );
    }
    
    // Check if item has pending orders
    const pendingOrders = await Order.countDocuments({
      'items.galleryItem': id,
      status: { $in: ['pending', 'confirmed', 'processing'] },
    });
    
    if (pendingOrders > 0) {
      return NextResponse.json(
        { error: 'Cannot delete item with pending orders' },
        { status: 400 }
      );
    }
    
    await GalleryItem.findByIdAndDelete(id);
    
    return NextResponse.json({ message: 'Gallery item deleted successfully' });
  } catch (error) {
    console.error('Error deleting gallery item:', error);
    return NextResponse.json(
      { error: 'Failed to delete gallery item' },
      { status: 500 }
    );
  }
}
