'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { isValidEmail, isValidPhone } from '../../lib/utils';

const GuestBookingForm = ({ bookingData, onSubmit, loading = false }) => {
  const [formData, setFormData] = useState({
    guestInfo: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      specialRequests: '',
    },
    createAccount: false,
    password: '',
    confirmPassword: '',
  });

  const [errors, setErrors] = useState({});

  const handleInputChange = (field, value) => {
    const keys = field.split('.');
    setFormData(prev => {
      const updated = { ...prev };
      let current = updated;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return updated;
    });

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Guest info validation
    if (!formData.guestInfo.firstName.trim()) {
      newErrors['guestInfo.firstName'] = 'First name is required';
    }

    if (!formData.guestInfo.lastName.trim()) {
      newErrors['guestInfo.lastName'] = 'Last name is required';
    }

    if (!formData.guestInfo.email.trim()) {
      newErrors['guestInfo.email'] = 'Email is required';
    } else if (!isValidEmail(formData.guestInfo.email)) {
      newErrors['guestInfo.email'] = 'Please enter a valid email';
    }

    if (!formData.guestInfo.phone.trim()) {
      newErrors['guestInfo.phone'] = 'Phone number is required';
    } else if (!isValidPhone(formData.guestInfo.phone)) {
      newErrors['guestInfo.phone'] = 'Please enter a valid phone number';
    }

    // Account creation validation
    if (formData.createAccount) {
      if (!formData.password) {
        newErrors.password = 'Password is required';
      } else if (formData.password.length < 6) {
        newErrors.password = 'Password must be at least 6 characters';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // Combine booking data with guest info
    const submitData = {
      ...bookingData,
      guestInfo: formData.guestInfo,
      createAccount: formData.createAccount,
      password: formData.password,
    };

    onSubmit(submitData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Guest Information */}
      <Card>
        <CardHeader>
          <CardTitle>Guest Information</CardTitle>
          <CardDescription>Please provide your contact details</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="First Name"
              value={formData.guestInfo.firstName}
              onChange={(e) => handleInputChange('guestInfo.firstName', e.target.value)}
              error={errors['guestInfo.firstName']}
              required
            />
            <Input
              label="Last Name"
              value={formData.guestInfo.lastName}
              onChange={(e) => handleInputChange('guestInfo.lastName', e.target.value)}
              error={errors['guestInfo.lastName']}
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Email"
              type="email"
              value={formData.guestInfo.email}
              onChange={(e) => handleInputChange('guestInfo.email', e.target.value)}
              error={errors['guestInfo.email']}
              required
            />
            <Input
              label="Phone"
              type="tel"
              value={formData.guestInfo.phone}
              onChange={(e) => handleInputChange('guestInfo.phone', e.target.value)}
              error={errors['guestInfo.phone']}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Special Requests (Optional)
            </label>
            <textarea
              value={formData.guestInfo.specialRequests}
              onChange={(e) => handleInputChange('guestInfo.specialRequests', e.target.value)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={3}
              placeholder="Any special requests or notes for your stay..."
            />
          </div>
        </CardContent>
      </Card>

      {/* Account Creation */}
      <Card>
        <CardHeader>
          <CardTitle>Account (Optional)</CardTitle>
          <CardDescription>Create an account to manage your bookings</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="createAccount"
              checked={formData.createAccount}
              onChange={(e) => handleInputChange('createAccount', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="createAccount" className="text-sm font-medium text-gray-700">
              Create an account to track your bookings and get exclusive offers
            </label>
          </div>

          {formData.createAccount && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                error={errors.password}
                required
                minLength={6}
              />
              <Input
                label="Confirm Password"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                error={errors.confirmPassword}
                required
              />
            </div>
          )}
        </CardContent>
      </Card>

      <Button
        type="submit"
        className="w-full"
        loading={loading}
        size="lg"
      >
        Continue to Payment
      </Button>

      <div className="text-center">
        <p className="text-xs text-gray-600">
          By proceeding, you agree to our{' '}
          <a href="/terms" className="text-blue-600 hover:text-blue-500">
            Terms of Service
          </a>{' '}
          and{' '}
          <a href="/privacy" className="text-blue-600 hover:text-blue-500">
            Privacy Policy
          </a>
        </p>
      </div>
    </form>
  );
};

export default GuestBookingForm;
