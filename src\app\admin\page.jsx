'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import Link from 'next/link';

const AdminDashboard = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState({
    lodges: { total: 0, active: 0 },
    bookings: { total: 0, pending: 0, confirmed: 0 },
    gallery: { total: 0, available: 0, sold: 0 },
    orders: { total: 0, pending: 0, completed: 0 },
    users: { total: 0, new: 0 },
    revenue: { total: 0, thisMonth: 0 },
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session || !session.user?.role || !['manager', 'admin'].includes(session.user.role)) {
      router.push('/');
      return;
    }
    
    fetchDashboardStats();
  }, [session, status, router]);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      
      // Fetch stats from various endpoints
      const [lodgesRes, bookingsRes, galleryRes, ordersRes, usersRes] = await Promise.all([
        fetch('/api/lodges?limit=1'),
        fetch('/api/bookings?limit=1'),
        fetch('/api/gallery?limit=1'),
        fetch('/api/orders?limit=1'),
        session.user.role === 'admin' ? fetch('/api/users?limit=1') : Promise.resolve({ json: () => ({ pagination: { total: 0 } }) }),
      ]);
      
      const [lodgesData, bookingsData, galleryData, ordersData, usersData] = await Promise.all([
        lodgesRes.json(),
        bookingsRes.json(),
        galleryRes.json(),
        ordersRes.json(),
        usersRes.json(),
      ]);
      
      setStats({
        lodges: {
          total: lodgesData.pagination?.total || 0,
          active: lodgesData.pagination?.total || 0, // Simplified for now
        },
        bookings: {
          total: bookingsData.pagination?.total || 0,
          pending: 0, // Would need separate API calls for status counts
          confirmed: 0,
        },
        gallery: {
          total: galleryData.pagination?.total || 0,
          available: 0,
          sold: 0,
        },
        orders: {
          total: ordersData.pagination?.total || 0,
          pending: 0,
          completed: 0,
        },
        users: {
          total: usersData.pagination?.total || 0,
          new: 0,
        },
        revenue: {
          total: 0, // Would need separate revenue API
          thisMonth: 0,
        },
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session || !['manager', 'admin'].includes(session.user?.role)) {
    return null;
  }

  const isAdmin = session.user.role === 'admin';

  const quickActions = [
    {
      title: 'Add New Lodge',
      description: 'Create a new lodge listing',
      href: '/admin/lodges/new',
      icon: '🏠',
      color: 'bg-blue-500',
    },
    {
      title: 'Add Gallery Item',
      description: 'Add new artwork to gallery',
      href: '/admin/gallery/new',
      icon: '🎨',
      color: 'bg-purple-500',
    },
    {
      title: 'View Bookings',
      description: 'Manage lodge bookings',
      href: '/admin/bookings',
      icon: '📅',
      color: 'bg-green-500',
    },
    {
      title: 'View Orders',
      description: 'Manage gallery orders',
      href: '/admin/orders',
      icon: '🛒',
      color: 'bg-orange-500',
    },
  ];

  if (isAdmin) {
    quickActions.push({
      title: 'Manage Users',
      description: 'User roles and permissions',
      href: '/admin/users',
      icon: '👥',
      color: 'bg-red-500',
    });
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            {isAdmin ? 'Admin' : 'Manager'} Dashboard
          </h1>
          <p className="text-gray-600 mt-2">
            Welcome back, {session.user.name}. Here's what's happening with your lodge management system.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Lodges</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.lodges.total}</div>
              <p className="text-sm text-gray-500">{stats.lodges.active} active</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Bookings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.bookings.total}</div>
              <p className="text-sm text-gray-500">{stats.bookings.pending} pending</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Gallery Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{stats.gallery.total}</div>
              <p className="text-sm text-gray-500">{stats.gallery.available} available</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Orders</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{stats.orders.total}</div>
              <p className="text-sm text-gray-500">{stats.orders.pending} pending</p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {quickActions.map((action, index) => (
              <Link key={index} href={action.href}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className={`w-10 h-10 rounded-lg ${action.color} flex items-center justify-center text-white text-lg`}>
                        {action.icon}
                      </div>
                      <div>
                        <CardTitle className="text-base">{action.title}</CardTitle>
                        <CardDescription className="text-sm">{action.description}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle>Recent Bookings</CardTitle>
              <CardDescription>Latest lodge reservations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-center text-gray-500 py-8">
                  <p>No recent bookings</p>
                  <Link href="/admin/bookings">
                    <Button variant="outline" className="mt-2">View All Bookings</Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Orders</CardTitle>
              <CardDescription>Latest gallery purchases</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-center text-gray-500 py-8">
                  <p>No recent orders</p>
                  <Link href="/admin/orders">
                    <Button variant="outline" className="mt-2">View All Orders</Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
