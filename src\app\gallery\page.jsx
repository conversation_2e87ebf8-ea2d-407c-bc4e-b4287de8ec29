'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';

const GalleryPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [priceRange, setPriceRange] = useState('all');

  // Mock data for gallery items
  const galleryItems = [
    {
      id: 1,
      title: 'Mountain Sunrise',
      artist: { name: '<PERSON>', bio: 'Landscape photographer' },
      category: 'photography',
      medium: 'Digital Photography',
      pricing: { price: 299, currency: 'USD', isForSale: true },
      images: [{ url: '/api/placeholder/400/400', alt: 'Mountain Sunrise', isPrimary: true }],
      description: 'A breathtaking capture of sunrise over the Rocky Mountains.',
      dimensions: { width: 24, height: 18, unit: 'inches' },
      status: 'available',
      featured: { isFeatured: true },
    },
    {
      id: 2,
      title: 'Forest Dreams',
      artist: { name: '<PERSON>', bio: 'Oil painter and nature enthusiast' },
      category: 'painting',
      medium: 'Oil on Canvas',
      pricing: { price: 1299, currency: 'USD', isForSale: true },
      images: [{ url: '/api/placeholder/400/400', alt: 'Forest Dreams', isPrimary: true }],
      description: 'An impressionistic painting of a mystical forest scene.',
      dimensions: { width: 36, height: 24, unit: 'inches' },
      status: 'available',
      featured: { isFeatured: false },
    },
    {
      id: 3,
      title: 'Ceramic Vase Collection',
      artist: { name: 'Elena Rodriguez', bio: 'Ceramic artist and sculptor' },
      category: 'crafts',
      medium: 'Glazed Ceramic',
      pricing: { price: 189, currency: 'USD', isForSale: true },
      images: [{ url: '/api/placeholder/400/400', alt: 'Ceramic Vase Collection', isPrimary: true }],
      description: 'Hand-thrown ceramic vases with unique glazing techniques.',
      dimensions: { width: 8, height: 12, unit: 'inches' },
      status: 'available',
      featured: { isFeatured: true },
    },
    {
      id: 4,
      title: 'Abstract Emotions',
      artist: { name: 'David Kim', bio: 'Contemporary abstract artist' },
      category: 'painting',
      medium: 'Acrylic on Canvas',
      pricing: { price: 899, currency: 'USD', isForSale: true },
      images: [{ url: '/api/placeholder/400/400', alt: 'Abstract Emotions', isPrimary: true }],
      description: 'A vibrant abstract piece exploring human emotions through color.',
      dimensions: { width: 30, height: 40, unit: 'inches' },
      status: 'available',
      featured: { isFeatured: false },
    },
    {
      id: 5,
      title: 'Wildlife Portrait',
      artist: { name: 'Lisa Thompson', bio: 'Wildlife photographer' },
      category: 'photography',
      medium: 'Fine Art Print',
      pricing: { price: 399, currency: 'USD', isForSale: true },
      images: [{ url: '/api/placeholder/400/400', alt: 'Wildlife Portrait', isPrimary: true }],
      description: 'Intimate portrait of a majestic eagle in its natural habitat.',
      dimensions: { width: 20, height: 30, unit: 'inches' },
      status: 'available',
      featured: { isFeatured: false },
    },
    {
      id: 6,
      title: 'Handwoven Tapestry',
      artist: { name: 'Maria Santos', bio: 'Textile artist' },
      category: 'textiles',
      medium: 'Wool and Silk',
      pricing: { price: 2499, currency: 'USD', isForSale: true },
      images: [{ url: '/api/placeholder/400/400', alt: 'Handwoven Tapestry', isPrimary: true }],
      description: 'Traditional handwoven tapestry with contemporary design elements.',
      dimensions: { width: 48, height: 60, unit: 'inches' },
      status: 'available',
      featured: { isFeatured: true },
    },
  ];

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'painting', label: 'Paintings' },
    { value: 'photography', label: 'Photography' },
    { value: 'sculpture', label: 'Sculptures' },
    { value: 'crafts', label: 'Crafts' },
    { value: 'textiles', label: 'Textiles' },
    { value: 'jewelry', label: 'Jewelry' },
  ];

  const priceRanges = [
    { value: 'all', label: 'All Prices' },
    { value: '0-500', label: 'Under $500' },
    { value: '500-1000', label: '$500 - $1,000' },
    { value: '1000-2000', label: '$1,000 - $2,000' },
    { value: '2000+', label: '$2,000+' },
  ];

  const filteredItems = galleryItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.artist.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    
    let matchesPrice = true;
    if (priceRange !== 'all') {
      const price = item.pricing.price;
      if (priceRange === '0-500') matchesPrice = price < 500;
      else if (priceRange === '500-1000') matchesPrice = price >= 500 && price <= 1000;
      else if (priceRange === '1000-2000') matchesPrice = price >= 1000 && price <= 2000;
      else if (priceRange === '2000+') matchesPrice = price > 2000;
    }
    
    return matchesSearch && matchesCategory && matchesPrice;
  });

  const featuredItems = filteredItems.filter(item => item.featured.isFeatured);

  const formatPrice = (price, currency) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(price);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Art Gallery
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover unique artworks from talented local and international artists. Each piece tells a story.
          </p>
        </div>

        {/* Featured Section */}
        {featuredItems.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Featured Artworks</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {featuredItems.slice(0, 3).map(item => (
                <Card key={item.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative">
                    <img
                      src={item.images[0].url}
                      alt={item.images[0].alt}
                      className="w-full h-64 object-cover"
                    />
                    <div className="absolute top-4 right-4 bg-blue-600 text-white px-2 py-1 rounded-md text-sm font-medium">
                      Featured
                    </div>
                  </div>
                  <CardHeader>
                    <CardTitle className="text-lg">{item.title}</CardTitle>
                    <CardDescription>by {item.artist.name}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{item.description}</p>
                    <div className="flex justify-between items-center">
                      <span className="text-2xl font-bold text-blue-600">
                        {formatPrice(item.pricing.price, item.pricing.currency)}
                      </span>
                      <Button>Add to Cart</Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Input
                placeholder="Search by title or artist..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                label="Search"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {categories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price Range
              </label>
              <select
                value={priceRange}
                onChange={(e) => setPriceRange(e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {priceRanges.map(range => (
                  <option key={range.value} value={range.value}>
                    {range.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Results */}
        <div className="mb-6">
          <p className="text-gray-600">
            {filteredItems.length} artwork{filteredItems.length !== 1 ? 's' : ''} found
          </p>
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredItems.map(item => (
            <Card key={item.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative">
                <img
                  src={item.images[0].url}
                  alt={item.images[0].alt}
                  className="w-full h-64 object-cover"
                />
                {item.status === 'sold' && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <span className="text-white text-lg font-bold">SOLD</span>
                  </div>
                )}
              </div>
              
              <CardHeader className="pb-2">
                <CardTitle className="text-base">{item.title}</CardTitle>
                <CardDescription className="text-sm">by {item.artist.name}</CardDescription>
              </CardHeader>

              <CardContent>
                <div className="space-y-2 mb-4">
                  <p className="text-sm text-gray-600">{item.medium}</p>
                  <p className="text-sm text-gray-500">
                    {item.dimensions.width}" × {item.dimensions.height}"
                  </p>
                  <span className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md capitalize">
                    {item.category}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-blue-600">
                    {formatPrice(item.pricing.price, item.pricing.currency)}
                  </span>
                  <Button 
                    size="sm" 
                    disabled={item.status === 'sold'}
                    variant={item.status === 'sold' ? 'outline' : 'default'}
                  >
                    {item.status === 'sold' ? 'Sold' : 'Add to Cart'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No artworks found matching your criteria.</p>
            <p className="text-gray-400 mt-2">Try adjusting your filters or search terms.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default GalleryPage;
