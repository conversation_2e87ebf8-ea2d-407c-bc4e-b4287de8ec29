#!/usr/bin/env node

/**
 * Auth.js Configuration Verification Script
 * 
 * This script verifies that all required environment variables
 * are properly configured for the Auth.js authentication system.
 */

require('dotenv').config({ path: '.env.local' });

const requiredVars = {
  'Core Auth.js': [
    'NEXTAUTH_URL',
    'NEXTAUTH_SECRET',
    'MONGODB_URI'
  ],
  'Google OAuth': [
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET'
  ],
  'Facebook OAuth': [
    'FACEBOOK_CLIENT_ID',
    'FACEBOOK_CLIENT_SECRET'
  ],
  'Email Configuration': [
    'EMAIL_SERVER_HOST',
    'EMAIL_SERVER_PORT',
    'EMAIL_SERVER_USER',
    'EMAIL_SERVER_PASSWORD',
    'EMAIL_FROM'
  ]
};

const optionalVars = [
  'RESEND_API_KEY',
  'ADMIN_EMAIL',
  'NEXTAUTH_DEBUG'
];

console.log('🔍 Auth.js Configuration Verification\n');

let allValid = true;
let warnings = [];

// Check required variables
Object.entries(requiredVars).forEach(([category, vars]) => {
  console.log(`📋 ${category}:`);
  
  vars.forEach(varName => {
    const value = process.env[varName];
    const isSet = value && value.trim() !== '';
    const isPlaceholder = value && (
      value.includes('your-') || 
      value.includes('placeholder') ||
      value === 'your-super-secret-key-here-minimum-32-characters'
    );
    
    if (!isSet) {
      console.log(`  ❌ ${varName}: Not set`);
      allValid = false;
    } else if (isPlaceholder) {
      console.log(`  ⚠️  ${varName}: Using placeholder value`);
      warnings.push(`${varName} is using a placeholder value`);
    } else {
      // Mask sensitive values
      const maskedValue = varName.includes('SECRET') || varName.includes('PASSWORD') 
        ? value.substring(0, 8) + '...' 
        : value.length > 50 
          ? value.substring(0, 50) + '...' 
          : value;
      console.log(`  ✅ ${varName}: ${maskedValue}`);
    }
  });
  
  console.log('');
});

// Check optional variables
console.log('📋 Optional Configuration:');
optionalVars.forEach(varName => {
  const value = process.env[varName];
  const isSet = value && value.trim() !== '';
  
  if (isSet) {
    const maskedValue = varName.includes('SECRET') || varName.includes('PASSWORD') 
      ? value.substring(0, 8) + '...' 
      : value.length > 50 
        ? value.substring(0, 50) + '...' 
        : value;
    console.log(`  ✅ ${varName}: ${maskedValue}`);
  } else {
    console.log(`  ⚪ ${varName}: Not set (optional)`);
  }
});

console.log('\n');

// Specific validations
console.log('🔍 Specific Validations:');

// NEXTAUTH_SECRET length
const secret = process.env.NEXTAUTH_SECRET;
if (secret && secret.length < 32) {
  console.log('  ❌ NEXTAUTH_SECRET: Must be at least 32 characters');
  allValid = false;
} else if (secret) {
  console.log('  ✅ NEXTAUTH_SECRET: Length is adequate');
}

// MongoDB URI format
const mongoUri = process.env.MONGODB_URI;
if (mongoUri && !mongoUri.startsWith('mongodb')) {
  console.log('  ❌ MONGODB_URI: Invalid format (should start with mongodb:// or mongodb+srv://)');
  allValid = false;
} else if (mongoUri) {
  console.log('  ✅ MONGODB_URI: Format looks correct');
}

// Email port validation
const emailPort = process.env.EMAIL_SERVER_PORT;
if (emailPort && (isNaN(emailPort) || emailPort < 1 || emailPort > 65535)) {
  console.log('  ❌ EMAIL_SERVER_PORT: Invalid port number');
  allValid = false;
} else if (emailPort) {
  console.log('  ✅ EMAIL_SERVER_PORT: Valid port number');
}

// Google OAuth credentials format
const googleClientId = process.env.GOOGLE_CLIENT_ID;
if (googleClientId && !googleClientId.includes('.apps.googleusercontent.com')) {
  console.log('  ⚠️  GOOGLE_CLIENT_ID: Format doesn\'t match expected Google format');
  warnings.push('Google Client ID format may be incorrect');
} else if (googleClientId) {
  console.log('  ✅ GOOGLE_CLIENT_ID: Format looks correct');
}

console.log('\n');

// Summary
if (allValid && warnings.length === 0) {
  console.log('🎉 All configurations are valid!');
  console.log('✅ Your Auth.js setup should work correctly.');
} else if (allValid && warnings.length > 0) {
  console.log('⚠️  Configuration is mostly valid with warnings:');
  warnings.forEach(warning => console.log(`   • ${warning}`));
  console.log('\n✅ Your Auth.js setup should work, but consider addressing the warnings.');
} else {
  console.log('❌ Configuration issues found!');
  console.log('🔧 Please fix the missing or invalid environment variables before proceeding.');
}

console.log('\n📚 For setup help, see: AUTH_SETUP.md');
console.log('🔧 For troubleshooting, see: AUTH_TROUBLESHOOTING.md');

process.exit(allValid ? 0 : 1);
