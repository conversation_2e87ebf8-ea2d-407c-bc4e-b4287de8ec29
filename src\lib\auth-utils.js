import { auth } from '../auth';
import connectDB from './mongodb';
import User from '../models/User';

/**
 * Get the current authenticated user with full database information
 */
export async function getCurrentUser() {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return null;
    }

    await connectDB();
    const user = await User.findById(session.user.id);
    
    if (!user) {
      return null;
    }

    return {
      id: user._id.toString(),
      email: user.email,
      name: user.name,
      role: user.role,
      image: user.image,
      profile: user.profile,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      // Helper methods
      hasRole: (role) => user.hasRole(role),
      canManageUsers: () => user.canManageUsers(),
      isGuest: () => user.isGuest(),
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Check if user has required role
 */
export async function hasRole(requiredRole) {
  const user = await getCurrentUser();
  return user ? user.hasRole(requiredRole) : false;
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated() {
  const session = await auth();
  return !!session?.user;
}

/**
 * Check if user is admin
 */
export async function isAdmin() {
  return await hasRole('admin');
}

/**
 * Check if user is manager or admin
 */
export async function isManager() {
  const user = await getCurrentUser();
  return user ? (user.role === 'manager' || user.role === 'admin') : false;
}

/**
 * Get user by ID with error handling
 */
export async function getUserById(userId) {
  try {
    await connectDB();
    const user = await User.findById(userId);
    return user;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
}

/**
 * Get user by email with error handling
 */
export async function getUserByEmail(email) {
  try {
    await connectDB();
    const user = await User.findOne({ email });
    return user;
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
}

/**
 * Create or update user from OAuth provider
 */
export async function createOrUpdateOAuthUser(profile, account) {
  try {
    await connectDB();
    
    let user = await User.findOne({ email: profile.email });
    
    if (!user) {
      // Determine role based on email
      const role = profile.email === process.env.ADMIN_EMAIL ? 'admin' : 'user';
      
      user = new User({
        name: profile.name,
        email: profile.email,
        image: profile.image,
        role,
        emailVerified: new Date(),
        accounts: [{
          provider: account.provider,
          providerAccountId: account.providerAccountId,
          type: account.type,
        }],
      });
      
      await user.save();
    } else {
      // Update existing user
      let updated = false;
      
      // Update admin role if needed
      if (profile.email === process.env.ADMIN_EMAIL && user.role !== 'admin') {
        user.role = 'admin';
        updated = true;
      }
      
      // Update profile info if missing
      if (profile.name && !user.name) {
        user.name = profile.name;
        updated = true;
      }
      
      if (profile.image && !user.image) {
        user.image = profile.image;
        updated = true;
      }
      
      // Add account if not exists
      const hasAccount = user.accounts?.some(
        acc => acc.provider === account.provider && acc.providerAccountId === account.providerAccountId
      );
      
      if (!hasAccount) {
        user.accounts = user.accounts || [];
        user.accounts.push({
          provider: account.provider,
          providerAccountId: account.providerAccountId,
          type: account.type,
        });
        updated = true;
      }
      
      if (updated) {
        await user.save();
      }
    }
    
    return user;
  } catch (error) {
    console.error('Error creating/updating OAuth user:', error);
    throw error;
  }
}

/**
 * Validate password strength
 */
export function validatePassword(password) {
  const errors = [];
  
  if (!password) {
    errors.push('Password is required');
    return errors;
  }
  
  if (password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }
  
  if (password.length > 128) {
    errors.push('Password must be less than 128 characters');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  return errors;
}

/**
 * Validate email format
 */
export function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Generate secure random token
 */
export function generateSecureToken(length = 32) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Rate limiting helper
 */
export class RateLimiter {
  constructor(maxRequests = 5, windowMs = 15 * 60 * 1000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
    this.requests = new Map();
  }
  
  isAllowed(identifier) {
    const now = Date.now();
    const userRequests = this.requests.get(identifier) || [];
    
    // Remove old requests outside the window
    const validRequests = userRequests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    // Add current request
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    return true;
  }
  
  getRemainingRequests(identifier) {
    const now = Date.now();
    const userRequests = this.requests.get(identifier) || [];
    const validRequests = userRequests.filter(time => now - time < this.windowMs);
    
    return Math.max(0, this.maxRequests - validRequests.length);
  }
  
  getResetTime(identifier) {
    const userRequests = this.requests.get(identifier) || [];
    if (userRequests.length === 0) return 0;
    
    const oldestRequest = Math.min(...userRequests);
    return oldestRequest + this.windowMs;
  }
}

/**
 * Sanitize user input
 */
export function sanitizeInput(input) {
  if (typeof input !== 'string') return input;
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential XSS characters
    .slice(0, 1000); // Limit length
}

/**
 * Log authentication events
 */
export function logAuthEvent(event, details = {}) {
  const timestamp = new Date().toISOString();
  const logData = {
    timestamp,
    event,
    ...details,
  };
  
  // In production, send to logging service
  if (process.env.NODE_ENV === 'development') {
    console.log('Auth Event:', logData);
  }
  
  // TODO: Implement proper logging service integration
  // - Send to CloudWatch, DataDog, or other logging service
  // - Store security events in database
  // - Set up alerts for suspicious activity
}

/**
 * Check if email domain is allowed
 */
export function isEmailDomainAllowed(email) {
  // Add domain restrictions if needed
  const blockedDomains = [
    'tempmail.org',
    '10minutemail.com',
    'guerrillamail.com',
  ];
  
  const domain = email.split('@')[1]?.toLowerCase();
  return !blockedDomains.includes(domain);
}

/**
 * Generate user avatar URL
 */
export function generateAvatarUrl(user) {
  if (user.image) {
    return user.image;
  }
  
  // Generate initials-based avatar
  const initials = user.name
    ?.split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2) || 'U';
  
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=3b82f6&color=ffffff&size=128`;
}
