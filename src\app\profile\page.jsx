'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { formatCurrency, formatDate } from '../../lib/utils';

const ProfilePage = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [user, setUser] = useState(null);
  const [bookings, setBookings] = useState([]);
  const [orders, setOrders] = useState([]);
  
  const [profileData, setProfileData] = useState({
    name: '',
    email: '',
    profile: {
      phone: '',
      address: {
        street1: '',
        street2: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'USA',
      },
      preferences: {
        newsletter: true,
        notifications: true,
      },
    },
  });

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/api/auth/signin');
      return;
    }
    
    fetchUserData();
  }, [session, status, router]);

  const fetchUserData = async () => {
    try {
      setLoading(true);
      
      // Fetch user profile
      const userResponse = await fetch(`/api/users/${session.user.id}`);
      const userData = await userResponse.json();
      
      if (userResponse.ok) {
        setUser(userData);
        setProfileData({
          name: userData.name || '',
          email: userData.email || '',
          profile: {
            phone: userData.profile?.phone || '',
            address: userData.profile?.address || {
              street1: '',
              street2: '',
              city: '',
              state: '',
              zipCode: '',
              country: 'USA',
            },
            preferences: userData.profile?.preferences || {
              newsletter: true,
              notifications: true,
            },
          },
        });
        
        // Set bookings and orders from populated data
        setBookings(userData.bookings || []);
        setOrders(userData.orders || []);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    try {
      setSaving(true);
      
      const response = await fetch(`/api/users/${session.user.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });
      
      if (response.ok) {
        alert('Profile updated successfully!');
        fetchUserData(); // Refresh data
      } else {
        const data = await response.json();
        alert(data.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      if (child.includes('.')) {
        const [subParent, subChild] = child.split('.');
        setProfileData(prev => ({
          ...prev,
          [parent]: {
            ...prev[parent],
            [subParent]: {
              ...prev[parent][subParent],
              [subChild]: value,
            },
          },
        }));
      } else {
        setProfileData(prev => ({
          ...prev,
          [parent]: {
            ...prev[parent],
            [child]: value,
          },
        }));
      }
    } else {
      setProfileData(prev => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  const tabs = [
    { id: 'profile', name: 'Profile', icon: '👤' },
    { id: 'bookings', name: 'Bookings', icon: '🏠' },
    { id: 'orders', name: 'Orders', icon: '🛒' },
  ];

  const getBookingStatusColor = (status) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-green-100 text-green-800',
      checked_in: 'bg-blue-100 text-blue-800',
      checked_out: 'bg-gray-100 text-gray-800',
      cancelled: 'bg-red-100 text-red-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getOrderStatusColor = (status) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-green-100 text-green-800',
      processing: 'bg-blue-100 text-blue-800',
      shipped: 'bg-purple-100 text-purple-800',
      delivered: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
          <p className="text-gray-600 mt-2">Manage your account settings and view your activity</p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.icon} {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Profile Tab */}
        {activeTab === 'profile' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>Personal Information</CardTitle>
                <CardDescription>Update your personal details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  label="Full Name"
                  value={profileData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                />
                <Input
                  label="Email"
                  type="email"
                  value={profileData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  disabled
                />
                <Input
                  label="Phone"
                  type="tel"
                  value={profileData.profile.phone}
                  onChange={(e) => handleInputChange('profile.phone', e.target.value)}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Address</CardTitle>
                <CardDescription>Your billing and shipping address</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  label="Street Address"
                  value={profileData.profile.address.street1}
                  onChange={(e) => handleInputChange('profile.address.street1', e.target.value)}
                />
                <Input
                  label="Apartment, suite, etc. (optional)"
                  value={profileData.profile.address.street2}
                  onChange={(e) => handleInputChange('profile.address.street2', e.target.value)}
                />
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="City"
                    value={profileData.profile.address.city}
                    onChange={(e) => handleInputChange('profile.address.city', e.target.value)}
                  />
                  <Input
                    label="State"
                    value={profileData.profile.address.state}
                    onChange={(e) => handleInputChange('profile.address.state', e.target.value)}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="ZIP Code"
                    value={profileData.profile.address.zipCode}
                    onChange={(e) => handleInputChange('profile.address.zipCode', e.target.value)}
                  />
                  <Input
                    label="Country"
                    value={profileData.profile.address.country}
                    onChange={(e) => handleInputChange('profile.address.country', e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Preferences</CardTitle>
                <CardDescription>Manage your communication preferences</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="newsletter"
                    checked={profileData.profile.preferences.newsletter}
                    onChange={(e) => handleInputChange('profile.preferences.newsletter', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="newsletter" className="text-sm font-medium text-gray-700">
                    Subscribe to newsletter
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="notifications"
                    checked={profileData.profile.preferences.notifications}
                    onChange={(e) => handleInputChange('profile.preferences.notifications', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="notifications" className="text-sm font-medium text-gray-700">
                    Receive booking notifications
                  </label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Account Information</CardTitle>
                <CardDescription>Your account details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-sm">
                  <p className="text-gray-600">Account Type</p>
                  <p className="font-medium capitalize">{user?.role}</p>
                </div>
                <div className="text-sm">
                  <p className="text-gray-600">Member Since</p>
                  <p className="font-medium">{formatDate(user?.createdAt)}</p>
                </div>
                <div className="text-sm">
                  <p className="text-gray-600">Email Verified</p>
                  <p className="font-medium">{user?.emailVerified ? 'Yes' : 'No'}</p>
                </div>
              </CardContent>
            </Card>

            <div className="lg:col-span-2">
              <Button onClick={handleSaveProfile} loading={saving} className="w-full">
                Save Changes
              </Button>
            </div>
          </div>
        )}

        {/* Bookings Tab */}
        {activeTab === 'bookings' && (
          <div className="space-y-4">
            {bookings.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center py-8">
                    <div className="text-6xl mb-4">🏠</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No bookings yet</h3>
                    <p className="text-gray-600 mb-4">Start exploring our amazing lodges</p>
                    <Button onClick={() => router.push('/lodges')}>
                      Browse Lodges
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              bookings.map(booking => (
                <Card key={booking._id}>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {booking.bookingNumber}
                        </h3>
                        <p className="text-gray-600">{booking.lodge?.name}</p>
                        <p className="text-sm text-gray-500">
                          {formatDate(booking.dates.checkIn)} - {formatDate(booking.dates.checkOut)}
                        </p>
                        <p className="text-lg font-bold text-blue-600 mt-2">
                          {formatCurrency(booking.pricing.total)}
                        </p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getBookingStatusColor(booking.status)}`}>
                        {booking.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )}

        {/* Orders Tab */}
        {activeTab === 'orders' && (
          <div className="space-y-4">
            {orders.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center py-8">
                    <div className="text-6xl mb-4">🛒</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No orders yet</h3>
                    <p className="text-gray-600 mb-4">Discover beautiful artworks in our gallery</p>
                    <Button onClick={() => router.push('/gallery')}>
                      Browse Gallery
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              orders.map(order => (
                <Card key={order._id}>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {order.orderNumber}
                        </h3>
                        <p className="text-gray-600">
                          {order.items?.length} item{order.items?.length !== 1 ? 's' : ''}
                        </p>
                        <p className="text-sm text-gray-500">
                          Ordered on {formatDate(order.createdAt)}
                        </p>
                        <p className="text-lg font-bold text-blue-600 mt-2">
                          {formatCurrency(order.pricing.total)}
                        </p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getOrderStatusColor(order.status)}`}>
                        {order.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfilePage;
