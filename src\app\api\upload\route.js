import { NextResponse } from 'next/server';
import { requireAuth, getCurrentUser } from '../../../lib/middleware';
import { uploadFile, uploadLodgeImages, uploadGalleryImages, uploadUserAvatar, validateImageFile } from '../../../lib/firebase';

// POST /api/upload - Upload files to Firebase Storage
export async function POST(request) {
  const authCheck = await requireAuth(request);
  if (authCheck) return authCheck;
  
  try {
    const user = await getCurrentUser();
    const formData = await request.formData();
    
    const files = formData.getAll('files');
    const type = formData.get('type'); // 'lodge', 'gallery', 'avatar', 'general'
    const entityId = formData.get('entityId'); // ID of lodge/gallery item if applicable
    
    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }
    
    if (!type) {
      return NextResponse.json(
        { error: 'Upload type is required' },
        { status: 400 }
      );
    }
    
    // Validate files
    for (const file of files) {
      try {
        validateImageFile(file, {
          maxSize: 10 * 1024 * 1024, // 10MB
          allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
        });
      } catch (error) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
    }
    
    let uploadedFiles = [];
    
    try {
      switch (type) {
        case 'lodge':
          if (!user.hasRole('manager')) {
            return NextResponse.json(
              { error: 'Insufficient permissions' },
              { status: 403 }
            );
          }
          if (!entityId) {
            return NextResponse.json(
              { error: 'Lodge ID is required' },
              { status: 400 }
            );
          }
          uploadedFiles = await uploadLodgeImages(files, entityId);
          break;
          
        case 'gallery':
          if (!user.hasRole('manager')) {
            return NextResponse.json(
              { error: 'Insufficient permissions' },
              { status: 403 }
            );
          }
          if (!entityId) {
            return NextResponse.json(
              { error: 'Gallery item ID is required' },
              { status: 400 }
            );
          }
          uploadedFiles = await uploadGalleryImages(files, entityId);
          break;
          
        case 'avatar':
          if (files.length > 1) {
            return NextResponse.json(
              { error: 'Only one avatar image allowed' },
              { status: 400 }
            );
          }
          const avatarUrl = await uploadUserAvatar(files[0], user.id);
          uploadedFiles = [{ url: avatarUrl, alt: 'User avatar' }];
          break;
          
        case 'general':
          // General file upload for authenticated users
          const uploadPromises = files.map(async (file, index) => {
            const fileName = `${Date.now()}_${index}_${file.name}`;
            const path = `uploads/${user.id}/${fileName}`;
            const url = await uploadFile(file, path);
            return { url, alt: file.name };
          });
          uploadedFiles = await Promise.all(uploadPromises);
          break;
          
        default:
          return NextResponse.json(
            { error: 'Invalid upload type' },
            { status: 400 }
          );
      }
      
      return NextResponse.json({
        message: 'Files uploaded successfully',
        files: uploadedFiles,
      });
    } catch (uploadError) {
      console.error('Upload error:', uploadError);
      return NextResponse.json(
        { error: 'Failed to upload files' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in upload API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
