import { NextResponse } from 'next/server';
import connectDB from '../../../lib/mongodb';
import Booking from '../../../models/Booking';
import Lodge from '../../../models/Lodge';
import { getCurrentUser, requireAuth, requireManager } from '../../../lib/middleware';
import { createBookingPaymentIntent } from '../../../lib/stripe';
import { createOrFindGuestUser, validateGuestBookingData } from '../../../lib/guestAuth';

// GET /api/bookings - Get user bookings or all bookings (admin)
export async function GET(request) {
  const authCheck = await requireAuth(request);
  if (authCheck) return authCheck;

  try {
    await connectDB();
    const user = await getCurrentUser();
    const { searchParams } = new URL(request.url);

    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const skip = (page - 1) * limit;

    let filter = {};

    // If not admin/manager, only show user's own bookings
    if (!user.hasRole('manager')) {
      filter.user = user.id;
    }

    // Filter by status
    const status = searchParams.get('status');
    if (status && status !== 'all') {
      filter.status = status;
    }

    // Filter by date range
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    if (startDate && endDate) {
      filter['dates.checkIn'] = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    }

    const bookings = await Booking.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('lodge', 'name location images pricing')
      .populate('user', 'name email');

    const total = await Booking.countDocuments(filter);

    return NextResponse.json({
      bookings,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching bookings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch bookings' },
      { status: 500 }
    );
  }
}

// POST /api/bookings - Create new booking (supports both authenticated and guest users)
export async function POST(request) {
  try {
    await connectDB();
    const data = await request.json();

    let user = null;
    let isGuestBooking = false;

    // Try to get current user (if authenticated)
    try {
      user = await getCurrentUser();
    } catch (error) {
      // User is not authenticated - this is a guest booking
      isGuestBooking = true;
    }

    // If guest booking, validate guest data and create/find user
    if (isGuestBooking) {
      const validationErrors = validateGuestBookingData(data);
      if (validationErrors.length > 0) {
        return NextResponse.json(
          { error: 'Validation failed', details: validationErrors },
          { status: 400 }
        );
      }

      // Create or find guest user
      user = await createOrFindGuestUser({
        email: data.guestInfo.email,
        firstName: data.guestInfo.firstName,
        lastName: data.guestInfo.lastName,
        phone: data.guestInfo.phone,
      });
    }

    // Validate required fields
    const requiredFields = ['lodge', 'dates', 'guests', 'guestInfo'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Get lodge and validate
    const lodge = await Lodge.findById(data.lodge);
    if (!lodge) {
      return NextResponse.json(
        { error: 'Lodge not found' },
        { status: 404 }
      );
    }

    // Calculate dates and pricing
    const checkIn = new Date(data.dates.checkIn);
    const checkOut = new Date(data.dates.checkOut);
    const nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));

    // Check availability
    if (!lodge.isAvailable(checkIn, checkOut)) {
      return NextResponse.json(
        { error: 'Lodge is not available for selected dates' },
        { status: 400 }
      );
    }

    // Calculate pricing
    const cost = lodge.calculateCost(nights);

    // Create booking
    const booking = new Booking({
      lodge: data.lodge,
      user: user.id,
      dates: {
        checkIn,
        checkOut,
        nights,
      },
      guests: data.guests,
      pricing: cost,
      guestInfo: data.guestInfo,
      status: 'pending',
    });

    await booking.save();

    // Create Stripe payment intent
    const paymentIntent = await createBookingPaymentIntent(booking);

    // Update booking with payment intent ID
    booking.payment.stripePaymentIntentId = paymentIntent.id;
    await booking.save();

    await booking.populate([
      { path: 'lodge', select: 'name location images' },
      { path: 'user', select: 'name email' },
    ]);

    return NextResponse.json({
      booking,
      clientSecret: paymentIntent.client_secret,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating booking:', error);
    return NextResponse.json(
      { error: 'Failed to create booking' },
      { status: 500 }
    );
  }
}
