import { NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// In-memory store for rate limiting
// In production, use Redis or another distributed store
const rateLimit = new Map();

// Rate limit configuration
const RATE_LIMIT = {
  // Max requests per window
  max: process.env.RATE_LIMIT_MAX ? parseInt(process.env.RATE_LIMIT_MAX) : 100,
  // Window size in ms (15 minutes)
  windowMs: process.env.RATE_LIMIT_WINDOW_MS ? parseInt(process.env.RATE_LIMIT_WINDOW_MS) : 15 * 60 * 1000,
};

// Paths that should be protected by authentication
const PROTECTED_PATHS = [
  '/api/bookings',
  '/api/orders',
  '/api/users',
  '/api/lodges/manage',
  '/api/gallery/manage',
  '/admin',
];

// Paths that should be rate limited more strictly (auth endpoints)
const AUTH_PATHS = [
  '/api/auth/signin',
  '/api/auth/callback',
  '/api/auth/signup',
];

export async function middleware(request) {
  const response = NextResponse.next();
  const { pathname } = request.nextUrl;
  
  // Add security headers to all responses
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  // Apply Content Security Policy in production
  if (process.env.NODE_ENV === 'production') {
    response.headers.set(
      'Content-Security-Policy',
      "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https://*.googleusercontent.com https://*.fbcdn.net https://firebasestorage.googleapis.com; connect-src 'self' https://api.stripe.com; frame-src 'self' https://js.stripe.com; font-src 'self';"
    );
  }
  
  // Apply rate limiting
  const ip = request.ip || 'unknown';
  const isAuthPath = AUTH_PATHS.some(path => pathname.startsWith(path));
  
  // Stricter rate limits for auth paths
  const currentLimit = isAuthPath 
    ? { ...RATE_LIMIT, max: Math.floor(RATE_LIMIT.max / 5) }
    : RATE_LIMIT;
  
  const rateLimitKey = `${ip}:${isAuthPath ? 'auth' : 'standard'}`;
  const now = Date.now();
  const rateData = rateLimit.get(rateLimitKey) || { count: 0, resetAt: now + currentLimit.windowMs };
  
  // Reset counter if window has passed
  if (now > rateData.resetAt) {
    rateData.count = 0;
    rateData.resetAt = now + currentLimit.windowMs;
  }
  
  // Increment counter
  rateData.count += 1;
  rateLimit.set(rateLimitKey, rateData);
  
  // Add rate limit headers
  response.headers.set('X-RateLimit-Limit', currentLimit.max.toString());
  response.headers.set('X-RateLimit-Remaining', Math.max(0, currentLimit.max - rateData.count).toString());
  response.headers.set('X-RateLimit-Reset', Math.ceil(rateData.resetAt / 1000).toString());
  
  // Check if rate limit exceeded
  if (rateData.count > currentLimit.max) {
    return new NextResponse(
      JSON.stringify({ error: 'Too many requests, please try again later' }),
      { 
        status: 429, 
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': Math.ceil((rateData.resetAt - now) / 1000).toString(),
          'X-RateLimit-Limit': currentLimit.max.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': Math.ceil(rateData.resetAt / 1000).toString(),
        }
      }
    );
  }
  
  // Check authentication for protected paths
  if (PROTECTED_PATHS.some(path => pathname.startsWith(path))) {
    const token = await getToken({ req: request });
    
    if (!token) {
      // For API routes, return JSON error
      if (pathname.startsWith('/api/')) {
        return new NextResponse(
          JSON.stringify({ error: 'Authentication required' }),
          { status: 401, headers: { 'Content-Type': 'application/json' } }
        );
      }
      
      // For page routes, redirect to login
      const url = new URL('/auth/signin', request.url);
      url.searchParams.set('callbackUrl', request.url);
      return NextResponse.redirect(url);
    }
    
    // Check for admin paths
    if (pathname.startsWith('/admin') && token.role !== 'admin' && token.role !== 'manager') {
      return new NextResponse(
        JSON.stringify({ error: 'Access denied' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      );
    }
  }
  
  return response;
}

export const config = {
  matcher: [
    // Apply to all routes except static files and _next
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:jpg|jpeg|gif|png|svg)).*)',
  ],
};
