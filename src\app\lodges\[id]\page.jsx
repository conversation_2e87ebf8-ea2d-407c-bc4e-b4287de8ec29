'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { formatCurrency, formatDate, formatDateRange, calculateNights } from '../../../lib/utils';
import GuestBookingForm from '../../../components/booking/GuestBookingForm';

const LodgeDetailPage = ({ params }) => {
  const { data: session } = useSession();
  const router = useRouter();
  const { id } = params;
  const [lodge, setLodge] = useState(null);
  const [loading, setLoading] = useState(true);
  const [bookingLoading, setBookingLoading] = useState(false);
  const [availability, setAvailability] = useState(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [bookingStep, setBookingStep] = useState('details'); // 'details', 'guest-info', 'payment'

  const [bookingData, setBookingData] = useState({
    checkIn: '',
    checkOut: '',
    guests: {
      adults: 2,
      children: 0,
      infants: 0,
    },
    guestInfo: {
      firstName: '',
      lastName: '',
      email: session?.user?.email || '',
      phone: '',
      specialRequests: '',
    },
  });

  useEffect(() => {
    fetchLodge();
  }, [id]);

  useEffect(() => {
    if (session?.user) {
      setBookingData(prev => ({
        ...prev,
        guestInfo: {
          ...prev.guestInfo,
          email: session.user.email,
          firstName: session.user.name?.split(' ')[0] || '',
          lastName: session.user.name?.split(' ').slice(1).join(' ') || '',
        },
      }));
    }
  }, [session]);

  useEffect(() => {
    if (bookingData.checkIn && bookingData.checkOut) {
      checkAvailability();
    }
  }, [bookingData.checkIn, bookingData.checkOut]);

  const fetchLodge = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/lodges/${id}`);
      const data = await response.json();

      if (response.ok) {
        setLodge(data);
      } else {
        console.error('Error fetching lodge:', data.error);
        router.push('/lodges');
      }
    } catch (error) {
      console.error('Error fetching lodge:', error);
      router.push('/lodges');
    } finally {
      setLoading(false);
    }
  };

  const checkAvailability = async () => {
    try {
      const params = new URLSearchParams({
        checkIn: bookingData.checkIn,
        checkOut: bookingData.checkOut,
      });

      const response = await fetch(`/api/lodges/${id}/availability?${params}`);
      const data = await response.json();

      setAvailability(data);
    } catch (error) {
      console.error('Error checking availability:', error);
    }
  };

  const handleBooking = async () => {
    if (!availability?.available) {
      alert('Selected dates are not available');
      return;
    }

    if (session) {
      // Authenticated user - proceed directly to booking
      try {
        setBookingLoading(true);

        const totalGuests = bookingData.guests.adults + bookingData.guests.children + bookingData.guests.infants;

        const response = await fetch('/api/bookings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            lodge: id,
            dates: {
              checkIn: bookingData.checkIn,
              checkOut: bookingData.checkOut,
            },
            guests: {
              ...bookingData.guests,
              total: totalGuests,
            },
            guestInfo: {
              firstName: session.user.name?.split(' ')[0] || '',
              lastName: session.user.name?.split(' ').slice(1).join(' ') || '',
              email: session.user.email,
              phone: '',
              specialRequests: bookingData.guestInfo.specialRequests || '',
            },
          }),
        });

        const data = await response.json();

        if (response.ok) {
          // Redirect to payment page with client secret
          router.push(`/booking/payment?booking=${data.booking._id}&client_secret=${data.clientSecret}`);
        } else {
          alert(data.error || 'Failed to create booking');
        }
      } catch (error) {
        console.error('Error creating booking:', error);
        alert('Failed to create booking');
      } finally {
        setBookingLoading(false);
      }
    } else {
      // Guest user - show guest info form
      setBookingStep('guest-info');
    }
  };

  const handleGuestBooking = async (guestBookingData) => {
    try {
      setBookingLoading(true);

      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(guestBookingData),
      });

      const data = await response.json();

      if (response.ok) {
        // Redirect to payment page with client secret
        router.push(`/booking/payment?booking=${data.booking._id}&client_secret=${data.clientSecret}`);
      } else {
        alert(data.error || 'Failed to create booking');
      }
    } catch (error) {
      console.error('Error creating booking:', error);
      alert('Failed to create booking');
    } finally {
      setBookingLoading(false);
    }
  };

  const formatAmenity = (amenity) => {
    return amenity.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!lodge) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Lodge Not Found</h1>
          <Button onClick={() => router.push('/lodges')}>
            Back to Lodges
          </Button>
        </div>
      </div>
    );
  }

  const totalGuests = bookingData.guests.adults + bookingData.guests.children + bookingData.guests.infants;
  const nights = bookingData.checkIn && bookingData.checkOut ? calculateNights(bookingData.checkIn, bookingData.checkOut) : 0;

  // Guest booking form
  if (bookingStep === 'guest-info') {
    const guestBookingData = {
      lodge: id,
      dates: {
        checkIn: bookingData.checkIn,
        checkOut: bookingData.checkOut,
      },
      guests: {
        ...bookingData.guests,
        total: totalGuests,
      },
    };

    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <Button
              variant="outline"
              onClick={() => setBookingStep('details')}
              className="mb-4"
            >
              ← Back to Lodge Details
            </Button>
            <h1 className="text-3xl font-bold text-gray-900">Complete Your Booking</h1>
            <p className="text-gray-600 mt-2">
              {lodge?.name} • {formatDateRange(bookingData.checkIn, bookingData.checkOut)} • {totalGuests} guests
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <GuestBookingForm
                bookingData={guestBookingData}
                onSubmit={handleGuestBooking}
                loading={bookingLoading}
              />
            </div>

            {/* Booking Summary */}
            <div className="lg:col-span-1">
              <Card className="sticky top-8">
                <CardHeader>
                  <CardTitle>Booking Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {lodge?.images && lodge.images.length > 0 && (
                    <img
                      src={lodge.images[0].url}
                      alt={lodge.images[0].alt}
                      className="w-full h-32 object-cover rounded-lg"
                    />
                  )}

                  <div>
                    <h3 className="font-semibold text-gray-900">{lodge?.name}</h3>
                    <p className="text-sm text-gray-600">{lodge?.location.city}, {lodge?.location.state}</p>
                  </div>

                  {availability?.available && nights > 0 && (
                    <div className="space-y-2 pt-4 border-t">
                      <div className="flex justify-between text-sm">
                        <span>{formatCurrency(lodge?.pricing.basePrice)} × {nights} nights</span>
                        <span>{formatCurrency(availability.pricing.subtotal)}</span>
                      </div>
                      {availability.pricing.cleaningFee > 0 && (
                        <div className="flex justify-between text-sm">
                          <span>Cleaning fee</span>
                          <span>{formatCurrency(availability.pricing.cleaningFee)}</span>
                        </div>
                      )}
                      {availability.pricing.serviceFee > 0 && (
                        <div className="flex justify-between text-sm">
                          <span>Service fee</span>
                          <span>{formatCurrency(availability.pricing.serviceFee)}</span>
                        </div>
                      )}
                      <div className="flex justify-between text-sm">
                        <span>Taxes</span>
                        <span>{formatCurrency(availability.pricing.tax)}</span>
                      </div>
                      <div className="flex justify-between font-semibold text-lg pt-2 border-t">
                        <span>Total</span>
                        <span>{formatCurrency(availability.pricing.total)}</span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <div className="mb-6">
          <Button variant="outline" onClick={() => router.push('/lodges')}>
            ← Back to Lodges
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Image Gallery */}
            <Card className="overflow-hidden">
              <div className="relative">
                {lodge.images && lodge.images.length > 0 && (
                  <>
                    <img
                      src={lodge.images[selectedImageIndex]?.url}
                      alt={lodge.images[selectedImageIndex]?.alt}
                      className="w-full h-96 object-cover"
                    />
                    {lodge.images.length > 1 && (
                      <div className="absolute bottom-4 left-4 flex space-x-2">
                        {lodge.images.map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setSelectedImageIndex(index)}
                            className={`w-3 h-3 rounded-full ${
                              index === selectedImageIndex ? 'bg-white' : 'bg-white/50'
                            }`}
                          />
                        ))}
                      </div>
                    )}
                  </>
                )}
              </div>

              {lodge.images && lodge.images.length > 1 && (
                <div className="p-4">
                  <div className="grid grid-cols-4 gap-2">
                    {lodge.images.slice(0, 4).map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedImageIndex(index)}
                        className={`relative rounded-lg overflow-hidden ${
                          index === selectedImageIndex ? 'ring-2 ring-blue-500' : ''
                        }`}
                      >
                        <img
                          src={image.url}
                          alt={image.alt}
                          className="w-full h-20 object-cover"
                        />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </Card>

            {/* Lodge Information */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-2xl">{lodge.name}</CardTitle>
                    <CardDescription className="text-lg">
                      {lodge.location.city}, {lodge.location.state}
                    </CardDescription>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-blue-600">
                      {formatCurrency(lodge.pricing.basePrice)}/night
                    </div>
                    {lodge.ratings.count > 0 && (
                      <div className="flex items-center mt-1">
                        <span className="text-yellow-400">★</span>
                        <span className="ml-1 text-sm text-gray-600">
                          {lodge.ratings.average} ({lodge.ratings.count} reviews)
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-6 text-sm text-gray-600">
                    <span>{lodge.capacity.guests} guests</span>
                    <span>{lodge.capacity.bedrooms} bedrooms</span>
                    <span>{lodge.capacity.bathrooms} bathrooms</span>
                    <span>{lodge.capacity.beds} beds</span>
                  </div>

                  <p className="text-gray-700">{lodge.description}</p>

                  {/* Amenities */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Amenities</h3>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {lodge.amenities.map(amenity => (
                        <div key={amenity} className="flex items-center text-sm text-gray-600">
                          <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                          {formatAmenity(amenity)}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Booking Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle>Book Your Stay</CardTitle>
                <CardDescription>
                  {formatCurrency(lodge.pricing.basePrice)} per night
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Date Selection */}
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    label="Check-in"
                    type="date"
                    value={bookingData.checkIn}
                    onChange={(e) => setBookingData(prev => ({ ...prev, checkIn: e.target.value }))}
                    min={new Date().toISOString().split('T')[0]}
                  />
                  <Input
                    label="Check-out"
                    type="date"
                    value={bookingData.checkOut}
                    onChange={(e) => setBookingData(prev => ({ ...prev, checkOut: e.target.value }))}
                    min={bookingData.checkIn || new Date().toISOString().split('T')[0]}
                  />
                </div>

                {/* Guest Selection */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Guests</label>
                  <div className="grid grid-cols-3 gap-2">
                    <div>
                      <label className="block text-xs text-gray-500">Adults</label>
                      <select
                        value={bookingData.guests.adults}
                        onChange={(e) => setBookingData(prev => ({
                          ...prev,
                          guests: { ...prev.guests, adults: parseInt(e.target.value) }
                        }))}
                        className="w-full rounded-md border border-gray-300 px-2 py-1 text-sm"
                      >
                        {[...Array(lodge.capacity.guests)].map((_, i) => (
                          <option key={i + 1} value={i + 1}>{i + 1}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs text-gray-500">Children</label>
                      <select
                        value={bookingData.guests.children}
                        onChange={(e) => setBookingData(prev => ({
                          ...prev,
                          guests: { ...prev.guests, children: parseInt(e.target.value) }
                        }))}
                        className="w-full rounded-md border border-gray-300 px-2 py-1 text-sm"
                      >
                        {[...Array(Math.max(0, lodge.capacity.guests - bookingData.guests.adults) + 1)].map((_, i) => (
                          <option key={i} value={i}>{i}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs text-gray-500">Infants</label>
                      <select
                        value={bookingData.guests.infants}
                        onChange={(e) => setBookingData(prev => ({
                          ...prev,
                          guests: { ...prev.guests, infants: parseInt(e.target.value) }
                        }))}
                        className="w-full rounded-md border border-gray-300 px-2 py-1 text-sm"
                      >
                        {[...Array(5)].map((_, i) => (
                          <option key={i} value={i}>{i}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>

                {/* Availability Check */}
                {availability && (
                  <div className={`p-3 rounded-md ${
                    availability.available ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                  }`}>
                    <p className={`text-sm font-medium ${
                      availability.available ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {availability.available ? 'Available!' : 'Not Available'}
                    </p>
                    {!availability.available && availability.reason && (
                      <p className="text-sm text-red-600 mt-1">{availability.reason}</p>
                    )}
                  </div>
                )}

                {/* Pricing Breakdown */}
                {availability?.available && nights > 0 && (
                  <div className="space-y-2 pt-4 border-t">
                    <div className="flex justify-between text-sm">
                      <span>{formatCurrency(lodge.pricing.basePrice)} × {nights} nights</span>
                      <span>{formatCurrency(availability.pricing.subtotal)}</span>
                    </div>
                    {availability.pricing.cleaningFee > 0 && (
                      <div className="flex justify-between text-sm">
                        <span>Cleaning fee</span>
                        <span>{formatCurrency(availability.pricing.cleaningFee)}</span>
                      </div>
                    )}
                    {availability.pricing.serviceFee > 0 && (
                      <div className="flex justify-between text-sm">
                        <span>Service fee</span>
                        <span>{formatCurrency(availability.pricing.serviceFee)}</span>
                      </div>
                    )}
                    <div className="flex justify-between text-sm">
                      <span>Taxes</span>
                      <span>{formatCurrency(availability.pricing.tax)}</span>
                    </div>
                    <div className="flex justify-between font-semibold text-lg pt-2 border-t">
                      <span>Total</span>
                      <span>{formatCurrency(availability.pricing.total)}</span>
                    </div>
                  </div>
                )}

                {/* Guest Information */}
                {availability?.available && (
                  <div className="space-y-3 pt-4 border-t">
                    <h4 className="font-medium text-gray-900">Guest Information</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        label="First Name"
                        value={bookingData.guestInfo.firstName}
                        onChange={(e) => setBookingData(prev => ({
                          ...prev,
                          guestInfo: { ...prev.guestInfo, firstName: e.target.value }
                        }))}
                        required
                      />
                      <Input
                        label="Last Name"
                        value={bookingData.guestInfo.lastName}
                        onChange={(e) => setBookingData(prev => ({
                          ...prev,
                          guestInfo: { ...prev.guestInfo, lastName: e.target.value }
                        }))}
                        required
                      />
                    </div>
                    <Input
                      label="Email"
                      type="email"
                      value={bookingData.guestInfo.email}
                      onChange={(e) => setBookingData(prev => ({
                        ...prev,
                        guestInfo: { ...prev.guestInfo, email: e.target.value }
                      }))}
                      required
                    />
                    <Input
                      label="Phone"
                      type="tel"
                      value={bookingData.guestInfo.phone}
                      onChange={(e) => setBookingData(prev => ({
                        ...prev,
                        guestInfo: { ...prev.guestInfo, phone: e.target.value }
                      }))}
                      required
                    />
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Special Requests (Optional)
                      </label>
                      <textarea
                        value={bookingData.guestInfo.specialRequests}
                        onChange={(e) => setBookingData(prev => ({
                          ...prev,
                          guestInfo: { ...prev.guestInfo, specialRequests: e.target.value }
                        }))}
                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                        rows={3}
                        placeholder="Any special requests or notes..."
                      />
                    </div>
                  </div>
                )}

                {/* Book Button */}
                <Button
                  className="w-full"
                  onClick={handleBooking}
                  disabled={!availability?.available || bookingLoading || totalGuests > lodge.capacity.guests}
                  loading={bookingLoading}
                >
                  {!availability?.available ? 'Check Availability' :
                   totalGuests > lodge.capacity.guests ? 'Too Many Guests' :
                   'Reserve Now'}
                </Button>

                {totalGuests > lodge.capacity.guests && (
                  <p className="text-sm text-red-600 text-center">
                    This lodge accommodates up to {lodge.capacity.guests} guests
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LodgeDetailPage;
