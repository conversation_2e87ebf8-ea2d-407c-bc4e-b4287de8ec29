import { NextResponse } from 'next/server';
import connectDB from '../../../../../lib/mongodb';
import Lodge from '../../../../../models/Lodge';
import Booking from '../../../../../models/Booking';

// GET /api/lodges/[id]/availability - Check availability for date range
export async function GET(request, { params }) {
  try {
    await connectDB();
    const { id } = params;
    const { searchParams } = new URL(request.url);
    
    const checkIn = searchParams.get('checkIn');
    const checkOut = searchParams.get('checkOut');
    
    if (!checkIn || !checkOut) {
      return NextResponse.json(
        { error: 'Check-in and check-out dates are required' },
        { status: 400 }
      );
    }
    
    const startDate = new Date(checkIn);
    const endDate = new Date(checkOut);
    
    if (startDate >= endDate) {
      return NextResponse.json(
        { error: 'Check-out date must be after check-in date' },
        { status: 400 }
      );
    }
    
    const lodge = await Lodge.findById(id);
    if (!lodge) {
      return NextResponse.json(
        { error: 'Lodge not found' },
        { status: 404 }
      );
    }
    
    // Check if lodge is active
    if (!lodge.availability.isActive) {
      return NextResponse.json({
        available: false,
        reason: 'Lodge is not currently available',
      });
    }
    
    // Check minimum/maximum stay requirements
    const nights = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
    if (nights < lodge.availability.minimumStay) {
      return NextResponse.json({
        available: false,
        reason: `Minimum stay is ${lodge.availability.minimumStay} nights`,
      });
    }
    
    if (nights > lodge.availability.maximumStay) {
      return NextResponse.json({
        available: false,
        reason: `Maximum stay is ${lodge.availability.maximumStay} nights`,
      });
    }
    
    // Check blocked dates
    for (const blocked of lodge.availability.blockedDates) {
      if (startDate <= new Date(blocked.endDate) && endDate >= new Date(blocked.startDate)) {
        return NextResponse.json({
          available: false,
          reason: blocked.reason || 'Dates are blocked',
        });
      }
    }
    
    // Check existing bookings
    const conflictingBookings = await Booking.find({
      lodge: id,
      status: { $in: ['confirmed', 'checked_in'] },
      $or: [
        {
          'dates.checkIn': { $lt: endDate },
          'dates.checkOut': { $gt: startDate },
        },
      ],
    });
    
    if (conflictingBookings.length > 0) {
      return NextResponse.json({
        available: false,
        reason: 'Dates are already booked',
      });
    }
    
    // Calculate pricing
    const cost = lodge.calculateCost(nights);
    
    return NextResponse.json({
      available: true,
      nights,
      pricing: cost,
      checkInTime: lodge.availability.checkInTime,
      checkOutTime: lodge.availability.checkOutTime,
    });
  } catch (error) {
    console.error('Error checking availability:', error);
    return NextResponse.json(
      { error: 'Failed to check availability' },
      { status: 500 }
    );
  }
}
