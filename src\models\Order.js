import mongoose from 'mongoose';

const OrderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    required: true,
    unique: true,
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  items: [{
    galleryItem: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'GalleryItem',
      required: true,
    },
    quantity: { type: Number, required: true, min: 1 },
    price: { type: Number, required: true }, // Price at time of purchase
    title: String, // Snapshot of item title
    artist: String, // Snapshot of artist name
    sku: String, // Snapshot of SKU
  }],
  pricing: {
    subtotal: { type: Number, required: true },
    shipping: { type: Number, default: 0 },
    tax: { type: Number, required: true },
    discount: { type: Number, default: 0 },
    total: { type: Number, required: true },
    currency: { type: String, default: 'USD' },
  },
  shipping: {
    address: {
      firstName: { type: String, required: true },
      lastName: { type: String, required: true },
      company: String,
      street1: { type: String, required: true },
      street2: String,
      city: { type: String, required: true },
      state: { type: String, required: true },
      zipCode: { type: String, required: true },
      country: { type: String, required: true, default: 'USA' },
      phone: String,
    },
    method: {
      type: String,
      enum: ['standard', 'expedited', 'overnight', 'pickup'],
      default: 'standard',
    },
    estimatedDelivery: Date,
    trackingNumber: String,
    carrier: String,
    notes: String,
  },
  billing: {
    address: {
      firstName: { type: String, required: true },
      lastName: { type: String, required: true },
      company: String,
      street1: { type: String, required: true },
      street2: String,
      city: { type: String, required: true },
      state: { type: String, required: true },
      zipCode: { type: String, required: true },
      country: { type: String, required: true, default: 'USA' },
    },
    sameAsShipping: { type: Boolean, default: true },
  },
  payment: {
    stripePaymentIntentId: String,
    stripeChargeId: String,
    method: {
      type: String,
      enum: ['card', 'bank_transfer', 'paypal', 'apple_pay', 'google_pay'],
      default: 'card',
    },
    status: {
      type: String,
      enum: ['pending', 'paid', 'failed', 'refunded', 'partially_refunded'],
      default: 'pending',
    },
    paidAt: Date,
    refundedAt: Date,
    refundAmount: Number,
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'],
    default: 'pending',
  },
  fulfillment: {
    processedAt: Date,
    shippedAt: Date,
    deliveredAt: Date,
    estimatedDelivery: Date,
    specialInstructions: String,
  },
  communication: [{
    type: {
      type: String,
      enum: ['email', 'sms', 'system'],
    },
    subject: String,
    message: String,
    sentAt: { type: Date, default: Date.now },
    sentBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  }],
  notes: {
    customer: String, // Customer notes
    internal: String, // Internal staff notes
  },
  cancellation: {
    cancelledAt: Date,
    cancelledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    reason: String,
    refundAmount: Number,
  },
}, {
  timestamps: true,
});

// Indexes for better performance
OrderSchema.index({ orderNumber: 1 });
OrderSchema.index({ user: 1 });
OrderSchema.index({ status: 1 });
OrderSchema.index({ 'payment.status': 1 });
OrderSchema.index({ createdAt: -1 });

// Pre-save middleware to generate order number
OrderSchema.pre('save', async function(next) {
  if (!this.orderNumber) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.orderNumber = `ORD-${timestamp}-${random}`.toUpperCase();
  }
  next();
});

// Virtual for customer full name
OrderSchema.virtual('customerName').get(function() {
  return `${this.shipping.address.firstName} ${this.shipping.address.lastName}`;
});

// Virtual for total items count
OrderSchema.virtual('totalItems').get(function() {
  return this.items.reduce((total, item) => total + item.quantity, 0);
});

// Method to check if order can be cancelled
OrderSchema.methods.canBeCancelled = function() {
  return ['pending', 'confirmed'].includes(this.status);
};

// Method to check if order can be refunded
OrderSchema.methods.canBeRefunded = function() {
  return ['shipped', 'delivered'].includes(this.status) && 
         this.payment.status === 'paid';
};

// Method to calculate refund amount
OrderSchema.methods.calculateRefund = function(items = null) {
  if (!this.canBeRefunded()) return 0;
  
  if (items) {
    // Partial refund for specific items
    return items.reduce((total, item) => {
      const orderItem = this.items.find(oi => oi._id.toString() === item.id);
      return total + (orderItem ? orderItem.price * item.quantity : 0);
    }, 0);
  }
  
  // Full refund
  return this.pricing.total;
};

// Method to update status with timestamp
OrderSchema.methods.updateStatus = function(newStatus) {
  this.status = newStatus;
  
  switch (newStatus) {
    case 'processing':
      this.fulfillment.processedAt = new Date();
      break;
    case 'shipped':
      this.fulfillment.shippedAt = new Date();
      break;
    case 'delivered':
      this.fulfillment.deliveredAt = new Date();
      break;
    case 'cancelled':
      this.cancellation.cancelledAt = new Date();
      break;
  }
};

export default mongoose.models.Order || mongoose.model('Order', OrderSchema);
