'use client';

import { Inter } from 'next/font/google';
import { SessionProvider } from 'next-auth/react';
import Layout from '../components/layout/Layout';
import { CartProvider } from '../contexts/CartContext';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export default function RootLayout({ children, session }) {
  return (
    <html lang="en">
      <body className={`${inter.className} antialiased`}>
        <SessionProvider session={session}>
          <CartProvider>
            <Layout>
              {children}
            </Layout>
          </CartProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
