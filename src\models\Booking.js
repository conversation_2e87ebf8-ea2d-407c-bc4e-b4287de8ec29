import mongoose from 'mongoose';

const BookingSchema = new mongoose.Schema({
  bookingNumber: {
    type: String,
    required: true,
    unique: true,
  },
  lodge: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Lodge',
    required: true,
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  dates: {
    checkIn: { type: Date, required: true },
    checkOut: { type: Date, required: true },
    nights: { type: Number, required: true },
  },
  guests: {
    adults: { type: Number, required: true, min: 1 },
    children: { type: Number, default: 0, min: 0 },
    infants: { type: Number, default: 0, min: 0 },
    total: { type: Number, required: true },
  },
  pricing: {
    basePrice: { type: Number, required: true },
    subtotal: { type: Number, required: true },
    cleaningFee: { type: Number, default: 0 },
    serviceFee: { type: Number, default: 0 },
    tax: { type: Number, required: true },
    total: { type: Number, required: true },
    currency: { type: String, default: 'USD' },
  },
  payment: {
    stripePaymentIntentId: String,
    stripeChargeId: String,
    status: {
      type: String,
      enum: ['pending', 'paid', 'failed', 'refunded', 'partially_refunded'],
      default: 'pending',
    },
    paidAt: Date,
    refundedAt: Date,
    refundAmount: Number,
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'checked_in', 'checked_out', 'cancelled', 'no_show'],
    default: 'pending',
  },
  guestInfo: {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    email: { type: String, required: true },
    phone: { type: String, required: true },
    specialRequests: String,
  },
  communication: [{
    type: {
      type: String,
      enum: ['email', 'sms', 'system'],
    },
    subject: String,
    message: String,
    sentAt: { type: Date, default: Date.now },
    sentBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  }],
  checkInOut: {
    actualCheckIn: Date,
    actualCheckOut: Date,
    keyCode: String,
    instructions: String,
  },
  review: {
    rating: { type: Number, min: 1, max: 5 },
    comment: String,
    reviewedAt: Date,
  },
  cancellation: {
    cancelledAt: Date,
    cancelledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    reason: String,
    refundAmount: Number,
  },
}, {
  timestamps: true,
});

// Indexes for better performance
BookingSchema.index({ bookingNumber: 1 });
BookingSchema.index({ user: 1 });
BookingSchema.index({ lodge: 1 });
BookingSchema.index({ 'dates.checkIn': 1, 'dates.checkOut': 1 });
BookingSchema.index({ status: 1 });
BookingSchema.index({ 'payment.status': 1 });

// Pre-save middleware to generate booking number
BookingSchema.pre('save', async function(next) {
  if (!this.bookingNumber) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.bookingNumber = `BK-${timestamp}-${random}`.toUpperCase();
  }
  next();
});

// Virtual for guest full name
BookingSchema.virtual('guestFullName').get(function() {
  return `${this.guestInfo.firstName} ${this.guestInfo.lastName}`;
});

// Method to check if booking can be cancelled
BookingSchema.methods.canBeCancelled = function() {
  const now = new Date();
  const checkIn = new Date(this.dates.checkIn);
  const hoursUntilCheckIn = (checkIn - now) / (1000 * 60 * 60);
  
  // Can cancel if more than 24 hours before check-in and not already cancelled
  return hoursUntilCheckIn > 24 && this.status !== 'cancelled';
};

// Method to calculate refund amount based on cancellation policy
BookingSchema.methods.calculateRefund = function(cancellationPolicy = 'moderate') {
  if (!this.canBeCancelled()) return 0;
  
  const now = new Date();
  const checkIn = new Date(this.dates.checkIn);
  const hoursUntilCheckIn = (checkIn - now) / (1000 * 60 * 60);
  
  let refundPercentage = 0;
  
  switch (cancellationPolicy) {
    case 'flexible':
      refundPercentage = hoursUntilCheckIn > 24 ? 1 : 0;
      break;
    case 'moderate':
      if (hoursUntilCheckIn > 120) refundPercentage = 1; // 5 days
      else if (hoursUntilCheckIn > 24) refundPercentage = 0.5;
      break;
    case 'strict':
      if (hoursUntilCheckIn > 168) refundPercentage = 1; // 7 days
      else if (hoursUntilCheckIn > 48) refundPercentage = 0.5;
      break;
  }
  
  return this.pricing.total * refundPercentage;
};

export default mongoose.models.Booking || mongoose.model('Booking', BookingSchema);
