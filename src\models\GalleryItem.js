import mongoose from 'mongoose';

const GalleryItemSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    required: true,
  },
  artist: {
    name: { type: String, required: true },
    bio: String,
    website: String,
    contact: String,
  },
  category: {
    type: String,
    enum: ['painting', 'sculpture', 'photography', 'digital_art', 'mixed_media', 'crafts', 'jewelry', 'textiles'],
    required: true,
  },
  medium: {
    type: String,
    required: true,
  },
  dimensions: {
    width: Number,
    height: Number,
    depth: Number,
    unit: { type: String, enum: ['inches', 'cm'], default: 'inches' },
  },
  pricing: {
    price: { type: Number, required: true, min: 0 },
    currency: { type: String, default: 'USD' },
    isForSale: { type: Boolean, default: true },
    originalPrice: Number, // For sale items
    discount: { type: Number, min: 0, max: 100 }, // Percentage discount
  },
  images: [{
    url: { type: String, required: true },
    alt: { type: String, required: true },
    isPrimary: { type: Boolean, default: false },
    order: { type: Number, default: 0 },
  }],
  inventory: {
    quantity: { type: Number, default: 1, min: 0 },
    isUnique: { type: Boolean, default: true }, // True for one-of-a-kind pieces
    sku: { type: String, unique: true },
  },
  details: {
    yearCreated: Number,
    materials: [String],
    techniques: [String],
    style: String,
    theme: String,
    isFramed: Boolean,
    weight: Number, // in pounds or kg
    careInstructions: String,
  },
  shipping: {
    isShippable: { type: Boolean, default: true },
    shippingCost: { type: Number, default: 0 },
    handlingTime: { type: Number, default: 3 }, // days
    dimensions: {
      length: Number,
      width: Number,
      height: Number,
      weight: Number,
    },
  },
  status: {
    type: String,
    enum: ['available', 'sold', 'reserved', 'on_hold', 'discontinued'],
    default: 'available',
  },
  featured: {
    isFeatured: { type: Boolean, default: false },
    featuredOrder: Number,
    featuredUntil: Date,
  },
  seo: {
    slug: { type: String, unique: true },
    metaTitle: String,
    metaDescription: String,
    tags: [String],
  },
  orders: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
}, {
  timestamps: true,
});

// Indexes for better performance
GalleryItemSchema.index({ category: 1 });
GalleryItemSchema.index({ 'pricing.price': 1 });
GalleryItemSchema.index({ status: 1 });
GalleryItemSchema.index({ 'featured.isFeatured': 1, 'featured.featuredOrder': 1 });
GalleryItemSchema.index({ 'seo.slug': 1 });
GalleryItemSchema.index({ 'seo.tags': 1 });

// Pre-save middleware to generate SKU and slug
GalleryItemSchema.pre('save', async function(next) {
  if (!this.inventory.sku) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 4);
    this.inventory.sku = `ART-${timestamp}-${random}`.toUpperCase();
  }
  
  if (!this.seo.slug) {
    this.seo.slug = this.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
  
  next();
});

// Virtual for discounted price
GalleryItemSchema.virtual('discountedPrice').get(function() {
  if (this.pricing.discount && this.pricing.discount > 0) {
    return this.pricing.price * (1 - this.pricing.discount / 100);
  }
  return this.pricing.price;
});

// Virtual for availability status
GalleryItemSchema.virtual('isAvailable').get(function() {
  return this.status === 'available' && 
         this.pricing.isForSale && 
         this.inventory.quantity > 0;
});

// Method to check if item can be purchased
GalleryItemSchema.methods.canBePurchased = function(quantity = 1) {
  return this.isAvailable && this.inventory.quantity >= quantity;
};

// Method to reserve item
GalleryItemSchema.methods.reserve = function() {
  if (this.status === 'available') {
    this.status = 'reserved';
    return true;
  }
  return false;
};

// Method to mark as sold
GalleryItemSchema.methods.markAsSold = function() {
  this.status = 'sold';
  this.inventory.quantity = 0;
};

export default mongoose.models.GalleryItem || mongoose.model('GalleryItem', GalleryItemSchema);
