'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { formatCurrency } from '../../../lib/utils';

const AdminGalleryPage = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({});

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session || !['manager', 'admin'].includes(session.user?.role)) {
      router.push('/');
      return;
    }
    
    fetchGalleryItems();
  }, [session, status, router, currentPage, searchTerm, selectedCategory, selectedStatus]);

  const fetchGalleryItems = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
      });
      
      if (searchTerm) params.append('search', searchTerm);
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      if (selectedStatus !== 'all') params.append('status', selectedStatus);
      
      const response = await fetch(`/api/gallery?${params}`);
      const data = await response.json();
      
      if (response.ok) {
        setItems(data.items);
        setPagination(data.pagination);
      } else {
        console.error('Error fetching gallery items:', data.error);
      }
    } catch (error) {
      console.error('Error fetching gallery items:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (itemId) => {
    if (!confirm('Are you sure you want to delete this gallery item?')) return;
    
    try {
      const response = await fetch(`/api/gallery/${itemId}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        fetchGalleryItems(); // Refresh the list
      } else {
        const data = await response.json();
        alert(data.error || 'Failed to delete gallery item');
      }
    } catch (error) {
      console.error('Error deleting gallery item:', error);
      alert('Failed to delete gallery item');
    }
  };

  const toggleFeatured = async (itemId, currentFeatured) => {
    try {
      const response = await fetch(`/api/gallery/${itemId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          featured: {
            isFeatured: !currentFeatured,
          },
        }),
      });
      
      if (response.ok) {
        fetchGalleryItems(); // Refresh the list
      } else {
        const data = await response.json();
        alert(data.error || 'Failed to update featured status');
      }
    } catch (error) {
      console.error('Error updating featured status:', error);
      alert('Failed to update featured status');
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session || !['manager', 'admin'].includes(session.user?.role)) {
    return null;
  }

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'painting', label: 'Paintings' },
    { value: 'photography', label: 'Photography' },
    { value: 'sculpture', label: 'Sculptures' },
    { value: 'crafts', label: 'Crafts' },
    { value: 'textiles', label: 'Textiles' },
    { value: 'jewelry', label: 'Jewelry' },
  ];

  const statuses = [
    { value: 'all', label: 'All Status' },
    { value: 'available', label: 'Available' },
    { value: 'sold', label: 'Sold' },
    { value: 'reserved', label: 'Reserved' },
    { value: 'on_hold', label: 'On Hold' },
    { value: 'discontinued', label: 'Discontinued' },
  ];

  const getStatusColor = (status) => {
    const colors = {
      available: 'bg-green-100 text-green-800',
      sold: 'bg-red-100 text-red-800',
      reserved: 'bg-yellow-100 text-yellow-800',
      on_hold: 'bg-orange-100 text-orange-800',
      discontinued: 'bg-gray-100 text-gray-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Manage Gallery</h1>
            <p className="text-gray-600 mt-2">Create, edit, and manage your art gallery</p>
          </div>
          <Link href="/admin/gallery/new">
            <Button>Add New Artwork</Button>
          </Link>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                placeholder="Search by title or artist..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                label="Search"
              />
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {categories.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {statuses.map(status => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {items.map(item => (
            <Card key={item._id} className="overflow-hidden">
              <div className="relative">
                {item.images && item.images.length > 0 && (
                  <img
                    src={item.images[0].url}
                    alt={item.images[0].alt}
                    className="w-full h-48 object-cover"
                  />
                )}
                <div className="absolute top-2 right-2 flex space-x-1">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                    {item.status.replace('_', ' ').toUpperCase()}
                  </span>
                  {item.featured?.isFeatured && (
                    <span className="px-2 py-1 bg-blue-500 text-white rounded-full text-xs font-medium">
                      Featured
                    </span>
                  )}
                </div>
              </div>
              
              <CardHeader className="pb-2">
                <CardTitle className="text-base">{item.title}</CardTitle>
                <CardDescription className="text-sm">by {item.artist.name}</CardDescription>
              </CardHeader>

              <CardContent>
                <div className="space-y-2 mb-4">
                  <p className="text-sm text-gray-600 capitalize">{item.category}</p>
                  <p className="text-sm text-gray-500">{item.medium}</p>
                  <p className="text-lg font-bold text-blue-600">
                    {formatCurrency(item.pricing.price)}
                  </p>
                  <p className="text-xs text-gray-500">SKU: {item.inventory?.sku}</p>
                </div>

                <div className="flex flex-col space-y-2">
                  <div className="flex space-x-2">
                    <Link href={`/admin/gallery/${item._id}/edit`} className="flex-1">
                      <Button variant="outline" size="sm" className="w-full">Edit</Button>
                    </Link>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleFeatured(item._id, item.featured?.isFeatured)}
                    >
                      {item.featured?.isFeatured ? 'Unfeature' : 'Feature'}
                    </Button>
                  </div>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(item._id)}
                  >
                    Delete
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {items.length === 0 && !loading && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <p className="text-gray-500 text-lg">No gallery items found</p>
                <p className="text-gray-400 mt-2">Add your first artwork to get started</p>
                <Link href="/admin/gallery/new">
                  <Button className="mt-4">Add New Artwork</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex space-x-2">
              <Button
                variant="outline"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
              >
                Previous
              </Button>
              <span className="flex items-center px-4 py-2 text-sm text-gray-700">
                Page {currentPage} of {pagination.pages}
              </span>
              <Button
                variant="outline"
                disabled={currentPage === pagination.pages}
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminGalleryPage;
