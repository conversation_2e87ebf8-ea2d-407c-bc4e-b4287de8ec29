import { NextResponse } from 'next/server';
import connectDB from '../../../../lib/mongodb';
import Lodge from '../../../../models/Lodge';
import Booking from '../../../../models/Booking';
import { getCurrentUser, requireManager } from '../../../../lib/middleware';

// GET /api/lodges/[id] - Get single lodge
export async function GET(request, { params }) {
  try {
    await connectDB();
    const { id } = params;
    
    const lodge = await Lodge.findById(id).populate('createdBy', 'name email');
    
    if (!lodge) {
      return NextResponse.json(
        { error: 'Lodge not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(lodge);
  } catch (error) {
    console.error('Error fetching lodge:', error);
    return NextResponse.json(
      { error: 'Failed to fetch lodge' },
      { status: 500 }
    );
  }
}

// PUT /api/lodges/[id] - Update lodge (manager/admin only)
export async function PUT(request, { params }) {
  const authCheck = await requireManager(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const { id } = params;
    const data = await request.json();
    
    const lodge = await Lodge.findById(id);
    if (!lodge) {
      return NextResponse.json(
        { error: 'Lodge not found' },
        { status: 404 }
      );
    }
    
    // Update lodge
    Object.assign(lodge, data);
    await lodge.save();
    await lodge.populate('createdBy', 'name email');
    
    return NextResponse.json(lodge);
  } catch (error) {
    console.error('Error updating lodge:', error);
    return NextResponse.json(
      { error: 'Failed to update lodge' },
      { status: 500 }
    );
  }
}

// DELETE /api/lodges/[id] - Delete lodge (manager/admin only)
export async function DELETE(request, { params }) {
  const authCheck = await requireManager(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const { id } = params;
    
    const lodge = await Lodge.findById(id);
    if (!lodge) {
      return NextResponse.json(
        { error: 'Lodge not found' },
        { status: 404 }
      );
    }
    
    // Check if lodge has active bookings
    const activeBookings = await Booking.countDocuments({
      lodge: id,
      status: { $in: ['pending', 'confirmed', 'checked_in'] },
    });
    
    if (activeBookings > 0) {
      return NextResponse.json(
        { error: 'Cannot delete lodge with active bookings' },
        { status: 400 }
      );
    }
    
    await Lodge.findByIdAndDelete(id);
    
    return NextResponse.json({ message: 'Lodge deleted successfully' });
  } catch (error) {
    console.error('Error deleting lodge:', error);
    return NextResponse.json(
      { error: 'Failed to delete lodge' },
      { status: 500 }
    );
  }
}
