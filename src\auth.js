import NextAuth from "next-auth"
import Google from "next-auth/providers/google"
import Facebook from "next-auth/providers/facebook"
import Credentials from "next-auth/providers/credentials"
import Nodemailer from "next-auth/providers/nodemailer"
import { MongoDBAdapter } from "@auth/mongodb-adapter"
import { MongoClient } from "mongodb"
import bcrypt from "bcryptjs"
import connectDB from "./lib/mongodb"
import User from "./models/User"

const client = new MongoClient(process.env.MONGODB_URI)
const clientPromise = client.connect()

export const { handlers, signIn, signOut, auth } = NextAuth({
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      allowDangerousEmailAccountLinking: true,
    }),
    Facebook({
      clientId: process.env.FACEBOOK_CLIENT_ID,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
      allowDangerousEmailAccountLinking: true,
    }),
    Nodemailer({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: parseInt(process.env.EMAIL_SERVER_PORT),
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
        secure: true, // Use SSL
      },
      from: process.env.EMAIL_FROM,
    }),
    Credentials({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        name: { label: "Name", type: "text" },
        isSignUp: { label: "Is Sign Up", type: "hidden" },
      },
      async authorize(credentials) {
        if (!credentials?.email) {
          throw new Error("Email is required")
        }

        await connectDB()

        // Handle sign up
        if (credentials.isSignUp === "true") {
          if (!credentials.password || !credentials.name) {
            throw new Error("Name and password are required for sign up")
          }

          const existingUser = await User.findOne({ email: credentials.email })
          if (existingUser) {
            throw new Error("User already exists with this email")
          }

          const hashedPassword = await bcrypt.hash(credentials.password, 12)

          // Determine role based on email
          const role = credentials.email === "<EMAIL>" ? "admin" : "user"

          const newUser = new User({
            name: credentials.name,
            email: credentials.email,
            password: hashedPassword,
            role,
            emailVerified: new Date(),
          })

          await newUser.save()

          return {
            id: newUser._id.toString(),
            email: newUser.email,
            name: newUser.name,
            role: newUser.role,
          }
        }

        // Handle sign in
        if (!credentials.password) {
          throw new Error("Password is required")
        }

        const user = await User.findOne({ email: credentials.email }).select("+password")

        if (!user || !user.password) {
          throw new Error("Invalid credentials")
        }

        const isPasswordValid = await bcrypt.compare(credentials.password, user.password)

        if (!isPasswordValid) {
          throw new Error("Invalid credentials")
        }

        // Track login activity
        await user.trackLogin()

        return {
          id: user._id.toString(),
          email: user.email,
          name: user.name,
          role: user.role,
        }
      },
    }),
  ],
  session: {
    strategy: "database",
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  callbacks: {
    async session({ session, user }) {
      // Connect to database and get user with role
      await connectDB()
      const dbUser = await User.findById(user.id)

      if (dbUser) {
        session.user.id = user.id
        session.user.role = dbUser.role
        session.user.profile = dbUser.profile

        // Track login activity
        await dbUser.trackLogin()
      }

      return session
    },
    async signIn({ user, account }) {
      // Connect to database
      await connectDB()

      try {
        // Check if user exists
        let existingUser = await User.findOne({ email: user.email })

        if (!existingUser) {
          // Determine role based on email
          const role = user.email === "<EMAIL>" ? "admin" : "user"

          // Create new user with appropriate role
          existingUser = new User({
            name: user.name,
            email: user.email,
            image: user.image,
            role,
            emailVerified: account?.provider === "email" ? null : new Date(),
          })
          await existingUser.save()
        } else {
          // Update existing user role if needed (for admin email)
          if (user.email === "<EMAIL>" && existingUser.role !== "admin") {
            existingUser.role = "admin"
            await existingUser.save()
          }

          // Update user info from OAuth if available
          if (account?.provider !== "credentials" && account?.provider !== "email") {
            let updated = false
            if (user.name && !existingUser.name) {
              existingUser.name = user.name
              updated = true
            }
            if (user.image && !existingUser.image) {
              existingUser.image = user.image
              updated = true
            }
            if (updated) {
              await existingUser.save()
            }
          }
        }

        return true
      } catch (error) {
        console.error("Error during sign in:", error)
        return false
      }
    },
  },
  pages: {
    signIn: "/auth/signin",
    signOut: "/auth/signout",
    error: "/auth/error",
    verifyRequest: "/auth/verify-request",
  },
  events: {
    async signIn({ user, account }) {
      console.log(`User signed in: ${user.email} via ${account?.provider}`)
    },
    async signOut({ session }) {
      console.log(`User signed out: ${session?.user?.email}`)
    },
    async createUser({ user }) {
      console.log(`New user created: ${user.email}`)
    },
  },
  debug: process.env.NODE_ENV === "development",
  trustHost: true,
})