import { NextResponse } from 'next/server';
import connectDB from '../../../lib/mongodb';
import User from '../../../models/User';
import { getCurrentUser, requireAdmin } from '../../../lib/middleware';

// GET /api/users - Get all users (admin only)
export async function GET(request) {
  const authCheck = await requireAdmin(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 20;
    const skip = (page - 1) * limit;
    
    // Build filter object
    const filter = {};
    
    // Search by name or email
    const search = searchParams.get('search');
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }
    
    // Filter by role
    const role = searchParams.get('role');
    if (role && role !== 'all') {
      filter.role = role;
    }
    
    // Sort options
    const sort = searchParams.get('sort') || 'createdAt';
    const order = searchParams.get('order') === 'asc' ? 1 : -1;
    const sortObj = { [sort]: order };
    
    const users = await User.find(filter)
      .select('-accounts -sessions') // Exclude sensitive data
      .sort(sortObj)
      .skip(skip)
      .limit(limit);
    
    const total = await User.countDocuments(filter);
    
    return NextResponse.json({
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}
