import { auth } from '../auth';
import { NextResponse } from 'next/server';
import connectDB from './mongodb';
import User from '../models/User';

// Role hierarchy for permission checking
const ROLE_HIERARCHY = {
  guest: 0,
  user: 1,
  manager: 2,
  admin: 3,
};

/**
 * Middleware to check if user has required role
 * @param {string} requiredRole - Minimum role required
 * @returns {Function} Middleware function
 */
export function requireRole(requiredRole) {
  return async function(request) {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userRole = session.user?.role || 'guest';
    const userLevel = ROLE_HIERARCHY[userRole] || 0;
    const requiredLevel = ROLE_HIERARCHY[requiredRole] || 0;

    if (userLevel < requiredLevel) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    return null; // Allow request to continue
  };
}

/**
 * Middleware to check if user is authenticated
 */
export async function requireAuth(request) {
  const session = await auth();

  if (!session) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    );
  }

  return null; // Allow request to continue
}

/**
 * Middleware to check if user can manage lodges
 */
export const requireManager = requireRole('manager');

/**
 * Middleware to check if user is admin
 */
export const requireAdmin = requireRole('admin');

/**
 * Helper function to get user from session
 */
export async function getCurrentUser() {
  const session = await auth();

  if (!session?.user) {
    return null;
  }

  // Get full user data from database
  await connectDB();
  const user = await User.findById(session.user.id);

  return user ? {
    id: user._id.toString(),
    email: user.email,
    name: user.name,
    role: user.role,
    profile: user.profile,
    hasRole: (role) => user.hasRole(role),
    canManageUsers: () => user.canManageUsers(),
  } : null;
}

/**
 * Helper function to check if user has specific permission
 */
export function hasPermission(userRole, requiredRole) {
  const userLevel = ROLE_HIERARCHY[userRole] || 0;
  const requiredLevel = ROLE_HIERARCHY[requiredRole] || 0;
  return userLevel >= requiredLevel;
}

/**
 * Wrapper for API routes that require authentication
 */
export function withAuth(handler, options = {}) {
  return async function(request, context) {
    const { requiredRole } = options;

    // Check authentication
    const authCheck = await requireAuth(request);
    if (authCheck) return authCheck;

    // Check role if specified
    if (requiredRole) {
      const roleCheck = await requireRole(requiredRole)(request);
      if (roleCheck) return roleCheck;
    }

    // Call the original handler
    return handler(request, context);
  };
}

/**
 * Wrapper for API routes that require manager role
 */
export function withManager(handler) {
  return withAuth(handler, { requiredRole: 'manager' });
}

/**
 * Wrapper for API routes that require admin role
 */
export function withAdmin(handler) {
  return withAuth(handler, { requiredRole: 'admin' });
}
