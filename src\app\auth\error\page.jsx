'use client';

import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';

const AuthErrorPage = () => {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');

  const getErrorMessage = (errorType) => {
    switch (errorType) {
      case 'Configuration':
        return {
          title: 'Server Configuration Error',
          message: 'There is a problem with the server configuration. Please contact support.',
          action: 'Contact Support',
        };
      case 'AccessDenied':
        return {
          title: 'Access Denied',
          message: 'You do not have permission to sign in with this account.',
          action: 'Try Different Account',
        };
      case 'Verification':
        return {
          title: 'Email Verification Required',
          message: 'Please check your email and click the verification link to complete sign in.',
          action: 'Resend Email',
        };
      case 'Default':
      case 'Signin':
        return {
          title: 'Sign In Error',
          message: 'An error occurred during sign in. Please try again.',
          action: 'Try Again',
        };
      case 'OAuthSignin':
        return {
          title: 'OAuth Sign In Error',
          message: 'Error occurred during OAuth sign in. Please try again.',
          action: 'Try Again',
        };
      case 'OAuthCallback':
        return {
          title: 'OAuth Callback Error',
          message: 'Error occurred during OAuth callback. Please try again.',
          action: 'Try Again',
        };
      case 'OAuthCreateAccount':
        return {
          title: 'Account Creation Error',
          message: 'Could not create OAuth account. Please try again or use a different method.',
          action: 'Try Different Method',
        };
      case 'EmailCreateAccount':
        return {
          title: 'Email Account Error',
          message: 'Could not create account with this email. Please try again.',
          action: 'Try Again',
        };
      case 'Callback':
        return {
          title: 'Callback Error',
          message: 'Error occurred during authentication callback.',
          action: 'Try Again',
        };
      case 'OAuthAccountNotLinked':
        return {
          title: 'Account Not Linked',
          message: 'This email is already associated with another account. Please sign in with your original method.',
          action: 'Sign In',
        };
      case 'EmailSignin':
        return {
          title: 'Email Sign In Error',
          message: 'Could not send email. Please check your email address and try again.',
          action: 'Try Again',
        };
      case 'CredentialsSignin':
        return {
          title: 'Invalid Credentials',
          message: 'The email or password you entered is incorrect. Please try again.',
          action: 'Try Again',
        };
      case 'SessionRequired':
        return {
          title: 'Session Required',
          message: 'You must be signed in to access this page.',
          action: 'Sign In',
        };
      default:
        return {
          title: 'Authentication Error',
          message: 'An unexpected error occurred. Please try again.',
          action: 'Try Again',
        };
    }
  };

  const errorInfo = getErrorMessage(error);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="text-6xl mb-4">⚠️</div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            {errorInfo.title}
          </h2>
        </div>

        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <p className="text-gray-600">
                {errorInfo.message}
              </p>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3">
                  <p className="text-sm text-red-600">
                    Error code: {error}
                  </p>
                </div>
              )}

              <div className="space-y-3">
                <Link href="/auth/signin">
                  <Button className="w-full">
                    {errorInfo.action}
                  </Button>
                </Link>

                <Link href="/">
                  <Button variant="outline" className="w-full">
                    Return Home
                  </Button>
                </Link>
              </div>

              <div className="text-center">
                <p className="text-sm text-gray-600">
                  Need help?{' '}
                  <Link href="/contact" className="text-blue-600 hover:text-blue-500">
                    Contact Support
                  </Link>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            If this problem persists, please contact our support team with the error code above.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthErrorPage;
