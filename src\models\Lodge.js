import mongoose from 'mongoose';

const LodgeSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    required: true,
  },
  shortDescription: {
    type: String,
    required: true,
    maxlength: 200,
  },
  type: {
    type: String,
    enum: ['cabin', 'lodge', 'suite', 'room', 'villa'],
    required: true,
  },
  capacity: {
    guests: { type: Number, required: true, min: 1 },
    bedrooms: { type: Number, required: true, min: 1 },
    bathrooms: { type: Number, required: true, min: 1 },
    beds: { type: Number, required: true, min: 1 },
  },
  pricing: {
    basePrice: { type: Number, required: true, min: 0 },
    currency: { type: String, default: 'USD' },
    cleaningFee: { type: Number, default: 0 },
    serviceFee: { type: Number, default: 0 },
    taxRate: { type: Number, default: 0.1 }, // 10% default tax
  },
  location: {
    address: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    zipCode: { type: String, required: true },
    country: { type: String, required: true, default: 'USA' },
    coordinates: {
      latitude: Number,
      longitude: Number,
    },
  },
  amenities: [{
    type: String,
    enum: [
      'wifi', 'kitchen', 'parking', 'pool', 'hot_tub', 'fireplace',
      'air_conditioning', 'heating', 'tv', 'washer', 'dryer',
      'pet_friendly', 'smoking_allowed', 'gym', 'spa', 'restaurant',
      'bar', 'room_service', 'concierge', 'business_center'
    ],
  }],
  images: [{
    url: { type: String, required: true },
    alt: { type: String, required: true },
    isPrimary: { type: Boolean, default: false },
    order: { type: Number, default: 0 },
  }],
  availability: {
    isActive: { type: Boolean, default: true },
    minimumStay: { type: Number, default: 1 },
    maximumStay: { type: Number, default: 30 },
    checkInTime: { type: String, default: '15:00' },
    checkOutTime: { type: String, default: '11:00' },
    blockedDates: [{
      startDate: Date,
      endDate: Date,
      reason: String,
    }],
  },
  rules: {
    checkInInstructions: String,
    houseRules: [String],
    cancellationPolicy: {
      type: String,
      enum: ['flexible', 'moderate', 'strict'],
      default: 'moderate',
    },
  },
  ratings: {
    average: { type: Number, default: 0, min: 0, max: 5 },
    count: { type: Number, default: 0 },
  },
  bookings: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
}, {
  timestamps: true,
});

// Indexes for better performance
LodgeSchema.index({ 'location.city': 1, 'location.state': 1 });
LodgeSchema.index({ type: 1 });
LodgeSchema.index({ 'pricing.basePrice': 1 });
LodgeSchema.index({ 'capacity.guests': 1 });
LodgeSchema.index({ 'availability.isActive': 1 });
LodgeSchema.index({ 'ratings.average': -1 });

// Virtual for total price calculation
LodgeSchema.virtual('totalPrice').get(function() {
  return this.pricing.basePrice + this.pricing.cleaningFee + this.pricing.serviceFee;
});

// Method to check availability for given dates
LodgeSchema.methods.isAvailable = function(startDate, endDate) {
  if (!this.availability.isActive) return false;
  
  // Check if dates fall within blocked periods
  for (const blocked of this.availability.blockedDates) {
    if (startDate <= blocked.endDate && endDate >= blocked.startDate) {
      return false;
    }
  }
  
  return true;
};

// Method to calculate total cost for a stay
LodgeSchema.methods.calculateCost = function(nights) {
  const subtotal = this.pricing.basePrice * nights;
  const fees = this.pricing.cleaningFee + this.pricing.serviceFee;
  const tax = (subtotal + fees) * this.pricing.taxRate;
  
  return {
    subtotal,
    cleaningFee: this.pricing.cleaningFee,
    serviceFee: this.pricing.serviceFee,
    tax,
    total: subtotal + fees + tax,
  };
};

export default mongoose.models.Lodge || mongoose.model('Lodge', LodgeSchema);
