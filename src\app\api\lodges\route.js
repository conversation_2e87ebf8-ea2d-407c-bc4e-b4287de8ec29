import { NextResponse } from 'next/server';
import connectDB from '../../../lib/mongodb';
import Lodge from '../../../models/Lodge';
import { getCurrentUser, requireAuth, requireManager } from '../../../lib/middleware';

// GET /api/lodges - List and filter lodges
export async function GET(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const skip = (page - 1) * limit;
    
    // Build filter object
    const filter = { 'availability.isActive': true };
    
    // Search by name or location
    const search = searchParams.get('search');
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { 'location.city': { $regex: search, $options: 'i' } },
        { 'location.state': { $regex: search, $options: 'i' } },
      ];
    }
    
    // Filter by type
    const type = searchParams.get('type');
    if (type && type !== 'all') {
      filter.type = type;
    }
    
    // Filter by price range
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    if (minPrice || maxPrice) {
      filter['pricing.basePrice'] = {};
      if (minPrice) filter['pricing.basePrice'].$gte = parseFloat(minPrice);
      if (maxPrice) filter['pricing.basePrice'].$lte = parseFloat(maxPrice);
    }
    
    // Filter by capacity
    const guests = searchParams.get('guests');
    if (guests) {
      filter['capacity.guests'] = { $gte: parseInt(guests) };
    }
    
    // Filter by amenities
    const amenities = searchParams.get('amenities');
    if (amenities) {
      const amenityList = amenities.split(',');
      filter.amenities = { $all: amenityList };
    }
    
    // Sort options
    const sort = searchParams.get('sort') || 'createdAt';
    const order = searchParams.get('order') === 'asc' ? 1 : -1;
    const sortObj = { [sort]: order };
    
    const lodges = await Lodge.find(filter)
      .sort(sortObj)
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'name email');
    
    const total = await Lodge.countDocuments(filter);
    
    return NextResponse.json({
      lodges,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching lodges:', error);
    return NextResponse.json(
      { error: 'Failed to fetch lodges' },
      { status: 500 }
    );
  }
}

// POST /api/lodges - Create new lodge (manager/admin only)
export async function POST(request) {
  const authCheck = await requireManager(request);
  if (authCheck) return authCheck;
  
  try {
    await connectDB();
    const user = await getCurrentUser();
    const data = await request.json();
    
    // Validate required fields
    const requiredFields = ['name', 'description', 'shortDescription', 'type', 'capacity', 'pricing', 'location'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }
    
    // Create lodge
    const lodge = new Lodge({
      ...data,
      createdBy: user.id,
    });
    
    await lodge.save();
    await lodge.populate('createdBy', 'name email');
    
    return NextResponse.json(lodge, { status: 201 });
  } catch (error) {
    console.error('Error creating lodge:', error);
    return NextResponse.json(
      { error: 'Failed to create lodge' },
      { status: 500 }
    );
  }
}
