'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

const LodgeForm = ({ lodgeId = null }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    shortDescription: '',
    type: 'cabin',
    capacity: {
      guests: 1,
      bedrooms: 1,
      bathrooms: 1,
      beds: 1,
    },
    pricing: {
      basePrice: 0,
      currency: 'USD',
      cleaningFee: 0,
      serviceFee: 0,
      taxRate: 0.1,
    },
    location: {
      address: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'USA',
    },
    amenities: [],
    images: [],
    availability: {
      isActive: true,
      minimumStay: 1,
      maximumStay: 30,
      checkInTime: '15:00',
      checkOutTime: '11:00',
    },
    rules: {
      checkInInstructions: '',
      houseRules: [],
      cancellationPolicy: 'moderate',
    },
  });

  const lodgeTypes = [
    { value: 'cabin', label: 'Cabin' },
    { value: 'lodge', label: 'Lodge' },
    { value: 'suite', label: 'Suite' },
    { value: 'room', label: 'Room' },
    { value: 'villa', label: 'Villa' },
  ];

  const availableAmenities = [
    'wifi', 'kitchen', 'parking', 'pool', 'hot_tub', 'fireplace',
    'air_conditioning', 'heating', 'tv', 'washer', 'dryer',
    'pet_friendly', 'smoking_allowed', 'gym', 'spa', 'restaurant',
    'bar', 'room_service', 'concierge', 'business_center'
  ];

  const cancellationPolicies = [
    { value: 'flexible', label: 'Flexible' },
    { value: 'moderate', label: 'Moderate' },
    { value: 'strict', label: 'Strict' },
  ];

  useEffect(() => {
    if (lodgeId) {
      fetchLodge();
    }
  }, [lodgeId]);

  const fetchLodge = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/lodges/${lodgeId}`);
      const data = await response.json();

      if (response.ok) {
        setFormData(data);
      } else {
        console.error('Error fetching lodge:', data.error);
      }
    } catch (error) {
      console.error('Error fetching lodge:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value,
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleAmenityToggle = (amenity) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity],
    }));
  };

  const handleImageUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    try {
      setUploading(true);
      const formDataUpload = new FormData();
      files.forEach(file => formDataUpload.append('files', file));
      formDataUpload.append('type', 'lodge');
      if (lodgeId) formDataUpload.append('entityId', lodgeId);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formDataUpload,
      });

      const data = await response.json();

      if (response.ok) {
        setFormData(prev => ({
          ...prev,
          images: [...prev.images, ...data.files],
        }));
      } else {
        alert(data.error || 'Failed to upload images');
      }
    } catch (error) {
      console.error('Error uploading images:', error);
      alert('Failed to upload images');
    } finally {
      setUploading(false);
    }
  };

  const removeImage = (index) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    try {
      setLoading(true);
      const url = lodgeId ? `/api/lodges/${lodgeId}` : '/api/lodges';
      const method = lodgeId ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok) {
        router.push('/admin/lodges');
      } else {
        alert(data.error || 'Failed to save lodge');
      }
    } catch (error) {
      console.error('Error saving lodge:', error);
      alert('Failed to save lodge');
    } finally {
      setLoading(false);
    }
  };

  const formatAmenity = (amenity) => {
    return amenity.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading && lodgeId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            {lodgeId ? 'Edit Lodge' : 'Create New Lodge'}
          </h1>
          <p className="text-gray-600 mt-2">
            {lodgeId ? 'Update lodge information' : 'Add a new lodge to your listings'}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>Essential details about your lodge</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                label="Lodge Name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Lodge Type
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => handleInputChange('type', e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  {lodgeTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Short Description
                </label>
                <textarea
                  value={formData.shortDescription}
                  onChange={(e) => handleInputChange('shortDescription', e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={2}
                  maxLength={200}
                  required
                />
                <p className="text-sm text-gray-500 mt-1">
                  {formData.shortDescription.length}/200 characters
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Full Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={4}
                  required
                />
              </div>
            </CardContent>
          </Card>

          {/* Capacity */}
          <Card>
            <CardHeader>
              <CardTitle>Capacity & Layout</CardTitle>
              <CardDescription>How many guests can your lodge accommodate?</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Input
                  label="Guests"
                  type="number"
                  min="1"
                  value={formData.capacity.guests}
                  onChange={(e) => handleInputChange('capacity.guests', parseInt(e.target.value))}
                  required
                />
                <Input
                  label="Bedrooms"
                  type="number"
                  min="1"
                  value={formData.capacity.bedrooms}
                  onChange={(e) => handleInputChange('capacity.bedrooms', parseInt(e.target.value))}
                  required
                />
                <Input
                  label="Bathrooms"
                  type="number"
                  min="1"
                  value={formData.capacity.bathrooms}
                  onChange={(e) => handleInputChange('capacity.bathrooms', parseInt(e.target.value))}
                  required
                />
                <Input
                  label="Beds"
                  type="number"
                  min="1"
                  value={formData.capacity.beds}
                  onChange={(e) => handleInputChange('capacity.beds', parseInt(e.target.value))}
                  required
                />
              </div>
            </CardContent>
          </Card>

          {/* Pricing */}
          <Card>
            <CardHeader>
              <CardTitle>Pricing</CardTitle>
              <CardDescription>Set your lodge pricing and fees</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Base Price per Night ($)"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.pricing.basePrice}
                  onChange={(e) => handleInputChange('pricing.basePrice', parseFloat(e.target.value))}
                  required
                />
                <Input
                  label="Cleaning Fee ($)"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.pricing.cleaningFee}
                  onChange={(e) => handleInputChange('pricing.cleaningFee', parseFloat(e.target.value))}
                />
                <Input
                  label="Service Fee ($)"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.pricing.serviceFee}
                  onChange={(e) => handleInputChange('pricing.serviceFee', parseFloat(e.target.value))}
                />
                <Input
                  label="Tax Rate (%)"
                  type="number"
                  min="0"
                  max="1"
                  step="0.01"
                  value={formData.pricing.taxRate}
                  onChange={(e) => handleInputChange('pricing.taxRate', parseFloat(e.target.value))}
                />
              </div>
            </CardContent>
          </Card>

          {/* Location */}
          <Card>
            <CardHeader>
              <CardTitle>Location</CardTitle>
              <CardDescription>Where is your lodge located?</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                label="Street Address"
                value={formData.location.address}
                onChange={(e) => handleInputChange('location.address', e.target.value)}
                required
              />
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Input
                  label="City"
                  value={formData.location.city}
                  onChange={(e) => handleInputChange('location.city', e.target.value)}
                  required
                />
                <Input
                  label="State"
                  value={formData.location.state}
                  onChange={(e) => handleInputChange('location.state', e.target.value)}
                  required
                />
                <Input
                  label="ZIP Code"
                  value={formData.location.zipCode}
                  onChange={(e) => handleInputChange('location.zipCode', e.target.value)}
                  required
                />
              </div>
            </CardContent>
          </Card>

          {/* Amenities */}
          <Card>
            <CardHeader>
              <CardTitle>Amenities</CardTitle>
              <CardDescription>What amenities does your lodge offer?</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {availableAmenities.map(amenity => (
                  <label key={amenity} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.amenities.includes(amenity)}
                      onChange={() => handleAmenityToggle(amenity)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{formatAmenity(amenity)}</span>
                  </label>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Images */}
          <Card>
            <CardHeader>
              <CardTitle>Images</CardTitle>
              <CardDescription>Upload photos of your lodge</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  disabled={uploading}
                />
                {uploading && <p className="text-sm text-blue-600 mt-2">Uploading images...</p>}
              </div>

              {formData.images.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {formData.images.map((image, index) => (
                    <div key={index} className="relative">
                      <img
                        src={image.url}
                        alt={image.alt}
                        className="w-full h-32 object-cover rounded-lg"
                      />
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600"
                      >
                        ×
                      </button>
                      {index === 0 && (
                        <span className="absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                          Primary
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Availability Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Availability Settings</CardTitle>
              <CardDescription>Configure booking rules and availability</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.availability.isActive}
                  onChange={(e) => handleInputChange('availability.isActive', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="isActive" className="text-sm font-medium text-gray-700">
                  Lodge is active and available for booking
                </label>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Input
                  label="Minimum Stay (nights)"
                  type="number"
                  min="1"
                  value={formData.availability.minimumStay}
                  onChange={(e) => handleInputChange('availability.minimumStay', parseInt(e.target.value))}
                />
                <Input
                  label="Maximum Stay (nights)"
                  type="number"
                  min="1"
                  value={formData.availability.maximumStay}
                  onChange={(e) => handleInputChange('availability.maximumStay', parseInt(e.target.value))}
                />
                <Input
                  label="Check-in Time"
                  type="time"
                  value={formData.availability.checkInTime}
                  onChange={(e) => handleInputChange('availability.checkInTime', e.target.value)}
                />
                <Input
                  label="Check-out Time"
                  type="time"
                  value={formData.availability.checkOutTime}
                  onChange={(e) => handleInputChange('availability.checkOutTime', e.target.value)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Rules & Policies */}
          <Card>
            <CardHeader>
              <CardTitle>Rules & Policies</CardTitle>
              <CardDescription>Set house rules and cancellation policy</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Cancellation Policy
                </label>
                <select
                  value={formData.rules.cancellationPolicy}
                  onChange={(e) => handleInputChange('rules.cancellationPolicy', e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {cancellationPolicies.map(policy => (
                    <option key={policy.value} value={policy.value}>
                      {policy.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Check-in Instructions
                </label>
                <textarea
                  value={formData.rules.checkInInstructions}
                  onChange={(e) => handleInputChange('rules.checkInInstructions', e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Provide detailed check-in instructions for guests..."
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/admin/lodges')}
            >
              Cancel
            </Button>
            <Button type="submit" loading={loading}>
              {lodgeId ? 'Update Lodge' : 'Create Lodge'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LodgeForm;
