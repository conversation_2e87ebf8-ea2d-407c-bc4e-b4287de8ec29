'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { isValidEmail, isValidPhone } from '../../lib/utils';

const GuestCheckoutForm = ({ onSubmit, loading = false }) => {
  const [formData, setFormData] = useState({
    billing: {
      address: {
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        street1: '',
        street2: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'USA',
      },
    },
    shipping: {
      address: {
        firstName: '',
        lastName: '',
        street1: '',
        street2: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'USA',
      },
      method: 'standard',
      sameAsBilling: true,
    },
    createAccount: false,
    password: '',
    confirmPassword: '',
  });

  const [errors, setErrors] = useState({});

  const handleInputChange = (field, value) => {
    const keys = field.split('.');
    setFormData(prev => {
      const updated = { ...prev };
      let current = updated;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return updated;
    });

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSameAsBillingChange = (checked) => {
    setFormData(prev => ({
      ...prev,
      shipping: {
        ...prev.shipping,
        sameAsBilling: checked,
        address: checked ? {
          firstName: prev.billing.address.firstName,
          lastName: prev.billing.address.lastName,
          street1: prev.billing.address.street1,
          street2: prev.billing.address.street2,
          city: prev.billing.address.city,
          state: prev.billing.address.state,
          zipCode: prev.billing.address.zipCode,
          country: prev.billing.address.country,
        } : prev.shipping.address,
      },
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    // Billing validation
    if (!formData.billing.address.firstName.trim()) {
      newErrors['billing.address.firstName'] = 'First name is required';
    }

    if (!formData.billing.address.lastName.trim()) {
      newErrors['billing.address.lastName'] = 'Last name is required';
    }

    if (!formData.billing.address.email.trim()) {
      newErrors['billing.address.email'] = 'Email is required';
    } else if (!isValidEmail(formData.billing.address.email)) {
      newErrors['billing.address.email'] = 'Please enter a valid email';
    }

    if (!formData.billing.address.phone.trim()) {
      newErrors['billing.address.phone'] = 'Phone number is required';
    } else if (!isValidPhone(formData.billing.address.phone)) {
      newErrors['billing.address.phone'] = 'Please enter a valid phone number';
    }

    if (!formData.billing.address.street1.trim()) {
      newErrors['billing.address.street1'] = 'Street address is required';
    }

    if (!formData.billing.address.city.trim()) {
      newErrors['billing.address.city'] = 'City is required';
    }

    if (!formData.billing.address.state.trim()) {
      newErrors['billing.address.state'] = 'State is required';
    }

    if (!formData.billing.address.zipCode.trim()) {
      newErrors['billing.address.zipCode'] = 'ZIP code is required';
    }

    // Shipping validation (if different from billing)
    if (!formData.shipping.sameAsBilling) {
      if (!formData.shipping.address.firstName.trim()) {
        newErrors['shipping.address.firstName'] = 'First name is required';
      }

      if (!formData.shipping.address.lastName.trim()) {
        newErrors['shipping.address.lastName'] = 'Last name is required';
      }

      if (!formData.shipping.address.street1.trim()) {
        newErrors['shipping.address.street1'] = 'Street address is required';
      }

      if (!formData.shipping.address.city.trim()) {
        newErrors['shipping.address.city'] = 'City is required';
      }

      if (!formData.shipping.address.state.trim()) {
        newErrors['shipping.address.state'] = 'State is required';
      }

      if (!formData.shipping.address.zipCode.trim()) {
        newErrors['shipping.address.zipCode'] = 'ZIP code is required';
      }
    }

    // Account creation validation
    if (formData.createAccount) {
      if (!formData.password) {
        newErrors.password = 'Password is required';
      } else if (formData.password.length < 6) {
        newErrors.password = 'Password must be at least 6 characters';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // Prepare data for submission
    const submitData = {
      ...formData,
      shipping: {
        ...formData.shipping,
        address: formData.shipping.sameAsBilling 
          ? formData.billing.address 
          : formData.shipping.address,
      },
    };

    onSubmit(submitData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Billing Information */}
      <Card>
        <CardHeader>
          <CardTitle>Billing Information</CardTitle>
          <CardDescription>Enter your billing details</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="First Name"
              value={formData.billing.address.firstName}
              onChange={(e) => handleInputChange('billing.address.firstName', e.target.value)}
              error={errors['billing.address.firstName']}
              required
            />
            <Input
              label="Last Name"
              value={formData.billing.address.lastName}
              onChange={(e) => handleInputChange('billing.address.lastName', e.target.value)}
              error={errors['billing.address.lastName']}
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Email"
              type="email"
              value={formData.billing.address.email}
              onChange={(e) => handleInputChange('billing.address.email', e.target.value)}
              error={errors['billing.address.email']}
              required
            />
            <Input
              label="Phone"
              type="tel"
              value={formData.billing.address.phone}
              onChange={(e) => handleInputChange('billing.address.phone', e.target.value)}
              error={errors['billing.address.phone']}
              required
            />
          </div>

          <Input
            label="Street Address"
            value={formData.billing.address.street1}
            onChange={(e) => handleInputChange('billing.address.street1', e.target.value)}
            error={errors['billing.address.street1']}
            required
          />

          <Input
            label="Apartment, suite, etc. (optional)"
            value={formData.billing.address.street2}
            onChange={(e) => handleInputChange('billing.address.street2', e.target.value)}
          />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              label="City"
              value={formData.billing.address.city}
              onChange={(e) => handleInputChange('billing.address.city', e.target.value)}
              error={errors['billing.address.city']}
              required
            />
            <Input
              label="State"
              value={formData.billing.address.state}
              onChange={(e) => handleInputChange('billing.address.state', e.target.value)}
              error={errors['billing.address.state']}
              required
            />
            <Input
              label="ZIP Code"
              value={formData.billing.address.zipCode}
              onChange={(e) => handleInputChange('billing.address.zipCode', e.target.value)}
              error={errors['billing.address.zipCode']}
              required
            />
          </div>
        </CardContent>
      </Card>

      {/* Shipping Information */}
      <Card>
        <CardHeader>
          <CardTitle>Shipping Information</CardTitle>
          <CardDescription>Where should we send your order?</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="sameAsBilling"
              checked={formData.shipping.sameAsBilling}
              onChange={(e) => handleSameAsBillingChange(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="sameAsBilling" className="text-sm font-medium text-gray-700">
              Same as billing address
            </label>
          </div>

          {!formData.shipping.sameAsBilling && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="First Name"
                  value={formData.shipping.address.firstName}
                  onChange={(e) => handleInputChange('shipping.address.firstName', e.target.value)}
                  error={errors['shipping.address.firstName']}
                  required
                />
                <Input
                  label="Last Name"
                  value={formData.shipping.address.lastName}
                  onChange={(e) => handleInputChange('shipping.address.lastName', e.target.value)}
                  error={errors['shipping.address.lastName']}
                  required
                />
              </div>

              <Input
                label="Street Address"
                value={formData.shipping.address.street1}
                onChange={(e) => handleInputChange('shipping.address.street1', e.target.value)}
                error={errors['shipping.address.street1']}
                required
              />

              <Input
                label="Apartment, suite, etc. (optional)"
                value={formData.shipping.address.street2}
                onChange={(e) => handleInputChange('shipping.address.street2', e.target.value)}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Input
                  label="City"
                  value={formData.shipping.address.city}
                  onChange={(e) => handleInputChange('shipping.address.city', e.target.value)}
                  error={errors['shipping.address.city']}
                  required
                />
                <Input
                  label="State"
                  value={formData.shipping.address.state}
                  onChange={(e) => handleInputChange('shipping.address.state', e.target.value)}
                  error={errors['shipping.address.state']}
                  required
                />
                <Input
                  label="ZIP Code"
                  value={formData.shipping.address.zipCode}
                  onChange={(e) => handleInputChange('shipping.address.zipCode', e.target.value)}
                  error={errors['shipping.address.zipCode']}
                  required
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Account Creation */}
      <Card>
        <CardHeader>
          <CardTitle>Account (Optional)</CardTitle>
          <CardDescription>Create an account to track your orders</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="createAccount"
              checked={formData.createAccount}
              onChange={(e) => handleInputChange('createAccount', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="createAccount" className="text-sm font-medium text-gray-700">
              Create an account for faster checkout next time
            </label>
          </div>

          {formData.createAccount && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                error={errors.password}
                required
                minLength={6}
              />
              <Input
                label="Confirm Password"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                error={errors.confirmPassword}
                required
              />
            </div>
          )}
        </CardContent>
      </Card>

      <Button
        type="submit"
        className="w-full"
        loading={loading}
        size="lg"
      >
        Continue to Payment
      </Button>
    </form>
  );
};

export default GuestCheckoutForm;
