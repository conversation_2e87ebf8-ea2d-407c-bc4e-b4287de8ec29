'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';

const LodgesPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [priceRange, setPriceRange] = useState('all');

  // Mock data for lodges
  const lodges = [
    {
      id: 1,
      name: 'Mountain View Lodge',
      type: 'lodge',
      description: 'A beautiful lodge with stunning mountain views and modern amenities.',
      shortDescription: 'Stunning mountain views with modern amenities',
      capacity: { guests: 8, bedrooms: 4, bathrooms: 3 },
      pricing: { basePrice: 299, currency: 'USD' },
      location: { city: 'Aspen', state: 'Colorado' },
      images: [{ url: '/api/placeholder/400/300', alt: 'Mountain View Lodge', isPrimary: true }],
      amenities: ['wifi', 'kitchen', 'parking', 'fireplace', 'hot_tub'],
      ratings: { average: 4.8, count: 124 },
    },
    {
      id: 2,
      name: 'Lakeside Cabin',
      type: 'cabin',
      description: 'Cozy cabin right by the lake with private dock and fishing access.',
      shortDescription: 'Cozy lakeside cabin with private dock',
      capacity: { guests: 6, bedrooms: 3, bathrooms: 2 },
      pricing: { basePrice: 199, currency: 'USD' },
      location: { city: 'Lake Tahoe', state: 'California' },
      images: [{ url: '/api/placeholder/400/300', alt: 'Lakeside Cabin', isPrimary: true }],
      amenities: ['wifi', 'kitchen', 'parking', 'fireplace'],
      ratings: { average: 4.6, count: 89 },
    },
    {
      id: 3,
      name: 'Forest Retreat Villa',
      type: 'villa',
      description: 'Luxury villa nestled in the forest with spa amenities and gourmet kitchen.',
      shortDescription: 'Luxury forest villa with spa amenities',
      capacity: { guests: 12, bedrooms: 6, bathrooms: 5 },
      pricing: { basePrice: 499, currency: 'USD' },
      location: { city: 'Big Sur', state: 'California' },
      images: [{ url: '/api/placeholder/400/300', alt: 'Forest Retreat Villa', isPrimary: true }],
      amenities: ['wifi', 'kitchen', 'parking', 'pool', 'hot_tub', 'spa', 'gym'],
      ratings: { average: 4.9, count: 67 },
    },
  ];

  const lodgeTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'cabin', label: 'Cabins' },
    { value: 'lodge', label: 'Lodges' },
    { value: 'villa', label: 'Villas' },
    { value: 'suite', label: 'Suites' },
  ];

  const priceRanges = [
    { value: 'all', label: 'All Prices' },
    { value: '0-200', label: 'Under $200' },
    { value: '200-400', label: '$200 - $400' },
    { value: '400+', label: '$400+' },
  ];

  const filteredLodges = lodges.filter(lodge => {
    const matchesSearch = lodge.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lodge.location.city.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || lodge.type === selectedType;
    
    let matchesPrice = true;
    if (priceRange !== 'all') {
      const price = lodge.pricing.basePrice;
      if (priceRange === '0-200') matchesPrice = price < 200;
      else if (priceRange === '200-400') matchesPrice = price >= 200 && price <= 400;
      else if (priceRange === '400+') matchesPrice = price > 400;
    }
    
    return matchesSearch && matchesType && matchesPrice;
  });

  const formatAmenity = (amenity) => {
    return amenity.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Discover Our Lodges
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Find the perfect accommodation for your next getaway. From cozy cabins to luxury villas.
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Input
                placeholder="Search by name or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                label="Search"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lodge Type
              </label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {lodgeTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price Range
              </label>
              <select
                value={priceRange}
                onChange={(e) => setPriceRange(e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {priceRanges.map(range => (
                  <option key={range.value} value={range.value}>
                    {range.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Results */}
        <div className="mb-6">
          <p className="text-gray-600">
            {filteredLodges.length} lodge{filteredLodges.length !== 1 ? 's' : ''} found
          </p>
        </div>

        {/* Lodge Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredLodges.map(lodge => (
            <Card key={lodge.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative">
                <img
                  src={lodge.images[0].url}
                  alt={lodge.images[0].alt}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-4 right-4 bg-white px-2 py-1 rounded-md text-sm font-medium">
                  ${lodge.pricing.basePrice}/night
                </div>
              </div>
              
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{lodge.name}</CardTitle>
                    <CardDescription>
                      {lodge.location.city}, {lodge.location.state}
                    </CardDescription>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span className="text-sm text-gray-600">
                      {lodge.ratings.average} ({lodge.ratings.count})
                    </span>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <p className="text-gray-600 mb-4">{lodge.shortDescription}</p>
                
                <div className="flex items-center text-sm text-gray-500 mb-4">
                  <span className="mr-4">{lodge.capacity.guests} guests</span>
                  <span className="mr-4">{lodge.capacity.bedrooms} bedrooms</span>
                  <span>{lodge.capacity.bathrooms} bathrooms</span>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  {lodge.amenities.slice(0, 3).map(amenity => (
                    <span
                      key={amenity}
                      className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md"
                    >
                      {formatAmenity(amenity)}
                    </span>
                  ))}
                  {lodge.amenities.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md">
                      +{lodge.amenities.length - 3} more
                    </span>
                  )}
                </div>

                <Button className="w-full">
                  View Details & Book
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredLodges.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No lodges found matching your criteria.</p>
            <p className="text-gray-400 mt-2">Try adjusting your filters or search terms.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default LodgesPage;
